const request = require('supertest');
const app = require('../src/app');

describe('API Tests', () => {
  // 健康检查测试
  describe('GET /api/health', () => {
    it('should return health status', async () => {
      const response = await request(app)
        .get('/api/health')
        .expect(200);
      
      expect(response.body).toHaveProperty('status', 'ok');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('version');
    });
  });

  // 青龙面板接口测试
  describe('Qinglong API', () => {
    const mockConfig = {
      baseURL: 'http://localhost:5700',
      clientId: 'test_client_id',
      clientSecret: 'test_client_secret'
    };

    it('should configure qinglong connection', async () => {
      const response = await request(app)
        .post('/api/qinglong/config')
        .send(mockConfig)
        .expect(200);
      
      expect(response.body).toHaveProperty('success', true);
    });

    it('should validate required fields for config', async () => {
      const response = await request(app)
        .post('/api/qinglong/config')
        .send({})
        .expect(400);
      
      expect(response.body).toHaveProperty('success', false);
    });
  });

  // AI服务接口测试
  describe('AI API', () => {
    const mockAIConfig = {
      provider: 'openai',
      apiKey: 'test_api_key',
      model: 'gpt-4'
    };

    it('should configure AI service', async () => {
      const response = await request(app)
        .post('/api/ai/config')
        .send(mockAIConfig)
        .expect(200);
      
      expect(response.body).toHaveProperty('success', true);
    });

    it('should get script templates', async () => {
      const response = await request(app)
        .get('/api/ai/templates')
        .expect(200);
      
      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toBeInstanceOf(Array);
    });
  });

  // 脚本管理接口测试
  describe('Script API', () => {
    it('should get local scripts', async () => {
      const response = await request(app)
        .get('/api/script/local')
        .expect(200);
      
      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toBeInstanceOf(Array);
    });

    it('should save local script', async () => {
      const scriptData = {
        filename: 'test.js',
        content: 'console.log("test");',
        description: 'Test script'
      };

      const response = await request(app)
        .post('/api/script/local')
        .send(scriptData)
        .expect(200);
      
      expect(response.body).toHaveProperty('success', true);
    });
  });

  // 日志管理接口测试
  describe('Log API', () => {
    it('should get memory logs', async () => {
      const response = await request(app)
        .get('/api/log/memory')
        .expect(200);
      
      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('logs');
      expect(response.body.data).toHaveProperty('total');
    });

    it('should get log statistics', async () => {
      const response = await request(app)
        .get('/api/log/stats')
        .expect(200);
      
      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('total');
      expect(response.body.data).toHaveProperty('levels');
    });

    it('should add custom log', async () => {
      const logData = {
        level: 'info',
        message: 'Test log message',
        source: 'test'
      };

      const response = await request(app)
        .post('/api/log/add')
        .send(logData)
        .expect(200);
      
      expect(response.body).toHaveProperty('success', true);
    });
  });

  // 错误处理测试
  describe('Error Handling', () => {
    it('should return 404 for non-existent routes', async () => {
      const response = await request(app)
        .get('/api/non-existent')
        .expect(404);
      
      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('message', '接口不存在');
    });

    it('should handle invalid JSON', async () => {
      const response = await request(app)
        .post('/api/qinglong/config')
        .set('Content-Type', 'application/json')
        .send('invalid json')
        .expect(400);
    });
  });
});

// WebSocket测试
describe('WebSocket Tests', () => {
  const io = require('socket.io-client');
  let clientSocket;
  let serverSocket;

  beforeAll((done) => {
    const server = require('http').createServer();
    const ioServer = require('socket.io')(server);
    
    server.listen(() => {
      const port = server.address().port;
      clientSocket = io(`http://localhost:${port}`);
      
      ioServer.on('connection', (socket) => {
        serverSocket = socket;
      });
      
      clientSocket.on('connect', done);
    });
  });

  afterAll(() => {
    if (clientSocket) {
      clientSocket.close();
    }
  });

  it('should connect to WebSocket', (done) => {
    expect(clientSocket.connected).toBe(true);
    done();
  });

  it('should join log room', (done) => {
    clientSocket.emit('join-log-room');
    
    serverSocket.on('join-log-room', () => {
      done();
    });
  });

  it('should receive new log events', (done) => {
    clientSocket.on('new-log', (log) => {
      expect(log).toHaveProperty('timestamp');
      expect(log).toHaveProperty('level');
      expect(log).toHaveProperty('message');
      done();
    });

    // 模拟发送日志
    serverSocket.emit('new-log', {
      timestamp: new Date().toISOString(),
      level: 'info',
      message: 'Test log',
      source: 'test'
    });
  });
});
