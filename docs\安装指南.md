# 青龙面板管理界面 - 安装指南

## 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0
- 青龙面板 >= 2.10.0

## 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd qinglong-manager
```

### 2. 安装依赖

```bash
# 安装后端依赖
cd backend
npm install

# 安装前端依赖
cd ../frontend
npm install
```

### 3. 配置环境变量

复制后端环境变量模板：

```bash
cd backend
cp .env.example .env
```

编辑 `.env` 文件，配置必要的环境变量：

```env
# 服务器配置
PORT=3000
HOST=0.0.0.0
NODE_ENV=development

# 日志配置
LOG_LEVEL=info

# CORS配置
CORS_ORIGINS=http://localhost:8080,http://127.0.0.1:8080
```

### 4. 启动服务

#### 开发模式

```bash
# 启动后端服务
cd backend
npm run dev

# 启动前端服务（新终端）
cd frontend
npm run dev
```

#### 生产模式

```bash
# 构建前端
cd frontend
npm run build

# 启动后端服务
cd ../backend
npm start
```

### 5. 访问应用

- 开发模式：http://localhost:8080
- 生产模式：http://localhost:3000

## 青龙面板配置

### 获取Client ID和Client Secret

1. 登录青龙面板
2. 进入"系统设置" → "应用设置"
3. 点击"新建应用"
4. 填写应用信息：
   - 应用名称：青龙管理界面
   - 权限范围：选择所需权限
5. 保存后获取Client ID和Client Secret

### 在管理界面中配置

1. 访问管理界面
2. 进入"连接设置"页面
3. 填写青龙面板信息：
   - 面板地址：http://your-qinglong-ip:5700
   - Client ID：从青龙面板获取
   - Client Secret：从青龙面板获取
4. 点击"测试连接"验证配置
5. 点击"保存配置"

## AI服务配置（可选）

### OpenAI配置

1. 获取OpenAI API密钥
2. 在"连接设置"中配置：
   - AI提供商：OpenAI GPT-4
   - API密钥：your-openai-api-key
   - API地址：https://api.openai.com/v1（默认）
   - 模型：gpt-4（默认）

### Claude配置

1. 获取Anthropic API密钥
2. 在"连接设置"中配置：
   - AI提供商：Anthropic Claude
   - API密钥：your-claude-api-key
   - API地址：https://api.anthropic.com/v1（默认）
   - 模型：claude-3-sonnet-20240229（默认）

### 本地Ollama配置

1. 安装并启动Ollama
2. 在"连接设置"中配置：
   - AI提供商：本地Ollama
   - API地址：http://localhost:11434/api（默认）
   - 模型：llama2（默认）

## 常见问题

### 1. 连接青龙面板失败

**问题**：提示"连接失败"或"认证失败"

**解决方案**：
- 检查青龙面板地址是否正确
- 确认Client ID和Client Secret是否有效
- 检查网络连接和防火墙设置
- 确认青龙面板版本支持OpenAPI

### 2. 前端页面无法访问

**问题**：浏览器显示"无法访问此网站"

**解决方案**：
- 检查前端服务是否正常启动
- 确认端口8080没有被占用
- 检查防火墙设置

### 3. AI功能无法使用

**问题**：AI脚本生成失败

**解决方案**：
- 检查AI服务配置是否正确
- 确认API密钥是否有效
- 检查网络连接（国外API可能需要代理）
- 查看后端日志获取详细错误信息

### 4. 脚本部署失败

**问题**：脚本上传到青龙面板失败

**解决方案**：
- 确认青龙面板连接正常
- 检查脚本文件格式是否正确
- 确认青龙面板有足够的存储空间
- 检查脚本权限设置

## 日志查看

### 后端日志

日志文件位置：`backend/logs/`

- `combined.log`：所有日志
- `error.log`：错误日志
- `exceptions.log`：异常日志

### 前端日志

打开浏览器开发者工具查看控制台日志。

## 性能优化

### 1. 生产环境配置

```env
NODE_ENV=production
LOG_LEVEL=warn
```

### 2. 反向代理配置

使用Nginx反向代理：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /socket.io/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 安全建议

1. **更改默认端口**：避免使用默认端口3000
2. **使用HTTPS**：在生产环境中启用SSL/TLS
3. **限制访问**：通过防火墙限制访问IP
4. **定期更新**：保持依赖包和系统更新
5. **备份数据**：定期备份配置和脚本文件

## 技术支持

如果遇到问题，请：

1. 查看本文档的常见问题部分
2. 检查后端日志文件
3. 在GitHub Issues中搜索相关问题
4. 提交新的Issue并提供详细信息

## 更新升级

### 更新代码

```bash
git pull origin main
```

### 更新依赖

```bash
# 更新后端依赖
cd backend
npm update

# 更新前端依赖
cd ../frontend
npm update
```

### 重启服务

```bash
# 重启后端服务
cd backend
npm restart

# 重新构建前端（如果需要）
cd ../frontend
npm run build
```
