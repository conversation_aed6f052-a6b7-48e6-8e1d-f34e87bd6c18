const fs = require('fs-extra');
const path = require('path');
const archiver = require('archiver');
const logger = require('../utils/logger');
const config = require('../config/config');
const qinglongService = require('./qinglongService');

class ScriptService {
  constructor() {
    this.scriptsDir = path.join(__dirname, '../../scripts');
    this.backupDir = path.join(__dirname, '../../backups');
    this.versionsDir = path.join(__dirname, '../../versions');
    
    // 确保目录存在
    this.ensureDirectories();
  }
  
  // 确保必要目录存在
  async ensureDirectories() {
    try {
      await fs.ensureDir(this.scriptsDir);
      await fs.ensureDir(this.backupDir);
      await fs.ensureDir(this.versionsDir);
    } catch (error) {
      logger.script(`创建目录失败: ${error.message}`, 'error');
    }
  }
  
  // 获取本地脚本列表
  async getLocalScripts() {
    try {
      await this.ensureDirectories();
      
      const files = await fs.readdir(this.scriptsDir);
      const scripts = [];
      
      for (const file of files) {
        if (this.isValidScriptFile(file)) {
          const filePath = path.join(this.scriptsDir, file);
          const stats = await fs.stat(filePath);
          
          // 尝试读取脚本元数据
          const metadata = await this.getScriptMetadata(file);
          
          scripts.push({
            filename: file,
            name: metadata.name || file,
            description: metadata.description || '',
            size: stats.size,
            modified: stats.mtime.toISOString(),
            created: stats.birthtime.toISOString(),
            language: this.getScriptLanguage(file),
            hasVersions: await this.hasVersionHistory(file)
          });
        }
      }
      
      // 按修改时间排序
      scripts.sort((a, b) => new Date(b.modified) - new Date(a.modified));
      
      logger.script(`获取本地脚本列表成功，共${scripts.length}个脚本`);
      
      return {
        success: true,
        data: scripts
      };
    } catch (error) {
      logger.script(`获取本地脚本列表失败: ${error.message}`, 'error');
      throw error;
    }
  }
  
  // 保存脚本到本地
  async saveLocalScript(filename, content, description = '') {
    try {
      await this.ensureDirectories();
      
      // 验证文件名
      if (!this.isValidScriptFile(filename)) {
        throw new Error('不支持的文件类型');
      }
      
      const filePath = path.join(this.scriptsDir, filename);
      
      // 如果文件已存在，先备份
      if (await fs.pathExists(filePath)) {
        await this.createVersionBackup(filename);
      }
      
      // 添加脚本元数据注释
      const metadata = {
        name: filename.replace(/\.[^/.]+$/, ''),
        description,
        created: new Date().toISOString(),
        modified: new Date().toISOString()
      };
      
      const contentWithMetadata = this.addMetadataToScript(content, metadata);
      
      // 保存文件
      await fs.writeFile(filePath, contentWithMetadata, 'utf8');
      
      logger.script(`脚本保存成功: ${filename}`);
      
      return {
        success: true,
        message: '脚本保存成功',
        data: {
          filename,
          size: Buffer.byteLength(contentWithMetadata, 'utf8')
        }
      };
    } catch (error) {
      logger.script(`保存脚本失败: ${error.message}`, 'error');
      throw error;
    }
  }
  
  // 获取本地脚本内容
  async getLocalScriptContent(filename) {
    try {
      const filePath = path.join(this.scriptsDir, filename);
      
      if (!await fs.pathExists(filePath)) {
        throw new Error('脚本文件不存在');
      }
      
      const content = await fs.readFile(filePath, 'utf8');
      const metadata = await this.getScriptMetadata(filename);
      
      return {
        success: true,
        data: {
          filename,
          content,
          metadata
        }
      };
    } catch (error) {
      logger.script(`获取脚本内容失败: ${error.message}`, 'error');
      throw error;
    }
  }
  
  // 删除本地脚本
  async deleteLocalScript(filename) {
    try {
      const filePath = path.join(this.scriptsDir, filename);
      
      if (!await fs.pathExists(filePath)) {
        throw new Error('脚本文件不存在');
      }
      
      // 创建删除前的备份
      await this.createVersionBackup(filename, 'deleted');
      
      // 删除文件
      await fs.remove(filePath);
      
      logger.script(`脚本删除成功: ${filename}`);
      
      return {
        success: true,
        message: '脚本删除成功'
      };
    } catch (error) {
      logger.script(`删除脚本失败: ${error.message}`, 'error');
      throw error;
    }
  }
  
  // 创建版本备份
  async createVersionBackup(filename, type = 'modified') {
    try {
      const filePath = path.join(this.scriptsDir, filename);
      
      if (!await fs.pathExists(filePath)) {
        return;
      }
      
      const content = await fs.readFile(filePath, 'utf8');
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const versionFilename = `${filename}.${timestamp}.${type}`;
      const versionPath = path.join(this.versionsDir, versionFilename);
      
      await fs.writeFile(versionPath, content, 'utf8');
      
      // 清理旧版本，保持最大版本数限制
      await this.cleanupOldVersions(filename);
      
      logger.script(`创建版本备份: ${versionFilename}`);
    } catch (error) {
      logger.script(`创建版本备份失败: ${error.message}`, 'error');
    }
  }
  
  // 清理旧版本
  async cleanupOldVersions(filename) {
    try {
      const files = await fs.readdir(this.versionsDir);
      const versionFiles = files
        .filter(file => file.startsWith(filename + '.'))
        .map(file => ({
          name: file,
          path: path.join(this.versionsDir, file),
          stats: null
        }));
      
      // 获取文件统计信息
      for (const file of versionFiles) {
        file.stats = await fs.stat(file.path);
      }
      
      // 按修改时间排序，保留最新的版本
      versionFiles.sort((a, b) => b.stats.mtime - a.stats.mtime);
      
      // 删除超出限制的版本
      const maxVersions = config.script.maxHistoryVersions;
      if (versionFiles.length > maxVersions) {
        const filesToDelete = versionFiles.slice(maxVersions);
        
        for (const file of filesToDelete) {
          await fs.remove(file.path);
          logger.script(`删除旧版本: ${file.name}`);
        }
      }
    } catch (error) {
      logger.script(`清理旧版本失败: ${error.message}`, 'error');
    }
  }
  
  // 获取脚本版本历史
  async getScriptVersions(filename) {
    try {
      const files = await fs.readdir(this.versionsDir);
      const versionFiles = files
        .filter(file => file.startsWith(filename + '.'))
        .map(file => {
          const parts = file.split('.');
          const timestamp = parts.slice(-2, -1)[0];
          const type = parts.slice(-1)[0];
          
          return {
            filename: file,
            originalFilename: filename,
            timestamp,
            type,
            date: new Date(timestamp.replace(/-/g, ':')).toISOString()
          };
        });
      
      // 按时间排序
      versionFiles.sort((a, b) => new Date(b.date) - new Date(a.date));
      
      // 获取文件大小信息
      for (const version of versionFiles) {
        const filePath = path.join(this.versionsDir, version.filename);
        const stats = await fs.stat(filePath);
        version.size = stats.size;
      }
      
      return {
        success: true,
        data: versionFiles
      };
    } catch (error) {
      logger.script(`获取版本历史失败: ${error.message}`, 'error');
      throw error;
    }
  }
  
  // 回滚脚本版本
  async rollbackScript(filename, versionFilename) {
    try {
      const versionPath = path.join(this.versionsDir, versionFilename);
      const scriptPath = path.join(this.scriptsDir, filename);
      
      if (!await fs.pathExists(versionPath)) {
        throw new Error('版本文件不存在');
      }
      
      // 备份当前版本
      if (await fs.pathExists(scriptPath)) {
        await this.createVersionBackup(filename, 'rollback-backup');
      }
      
      // 复制版本文件到脚本目录
      const content = await fs.readFile(versionPath, 'utf8');
      await fs.writeFile(scriptPath, content, 'utf8');
      
      logger.script(`脚本回滚成功: ${filename} -> ${versionFilename}`);
      
      return {
        success: true,
        message: '脚本回滚成功'
      };
    } catch (error) {
      logger.script(`回滚脚本失败: ${error.message}`, 'error');
      throw error;
    }
  }
  
  // 批量部署脚本
  async batchDeploy(scripts) {
    try {
      const results = [];
      
      for (const script of scripts) {
        try {
          const { filename, createCron = false, cronConfig = null } = script;
          
          // 获取脚本内容
          const scriptContent = await this.getLocalScriptContent(filename);
          
          // 上传到青龙面板
          await qinglongService.uploadScript(filename, scriptContent.data.content);
          
          // 如果需要创建定时任务
          if (createCron && cronConfig) {
            await qinglongService.addCron({
              name: cronConfig.name || filename,
              command: `node ${filename}`,
              schedule: cronConfig.schedule,
              ...cronConfig
            });
          }
          
          results.push({
            filename,
            success: true,
            message: '部署成功'
          });
          
          logger.script(`脚本部署成功: ${filename}`);
        } catch (error) {
          results.push({
            filename: script.filename,
            success: false,
            message: error.message
          });
          
          logger.script(`脚本部署失败: ${script.filename} - ${error.message}`, 'error');
        }
      }
      
      const successCount = results.filter(r => r.success).length;
      const totalCount = results.length;
      
      return {
        success: true,
        message: `批量部署完成，成功${successCount}/${totalCount}个脚本`,
        data: results
      };
    } catch (error) {
      logger.script(`批量部署失败: ${error.message}`, 'error');
      throw error;
    }
  }
  
  // 检查脚本依赖
  async checkDependencies(content) {
    try {
      const dependencies = [];
      const envVariables = [];
      
      // 检查Node.js模块依赖
      const requireRegex = /require\(['"`]([^'"`]+)['"`]\)/g;
      const importRegex = /import\s+.*\s+from\s+['"`]([^'"`]+)['"`]/g;
      
      let match;
      
      // 检查require语句
      while ((match = requireRegex.exec(content)) !== null) {
        const module = match[1];
        if (!module.startsWith('./') && !module.startsWith('../')) {
          dependencies.push({
            name: module,
            type: 'npm',
            required: true
          });
        }
      }
      
      // 检查import语句
      while ((match = importRegex.exec(content)) !== null) {
        const module = match[1];
        if (!module.startsWith('./') && !module.startsWith('../')) {
          dependencies.push({
            name: module,
            type: 'npm',
            required: true
          });
        }
      }
      
      // 检查环境变量
      const envRegex = /process\.env\.([A-Z_][A-Z0-9_]*)/g;
      while ((match = envRegex.exec(content)) !== null) {
        const envVar = match[1];
        if (!envVariables.includes(envVar)) {
          envVariables.push(envVar);
        }
      }
      
      // 去重
      const uniqueDependencies = dependencies.filter((dep, index, self) => 
        index === self.findIndex(d => d.name === dep.name)
      );
      
      return {
        success: true,
        data: {
          dependencies: uniqueDependencies,
          envVariables,
          hasExternalDependencies: uniqueDependencies.length > 0,
          hasEnvVariables: envVariables.length > 0
        }
      };
    } catch (error) {
      logger.script(`检查依赖失败: ${error.message}`, 'error');
      throw error;
    }
  }
  
  // 生成Cron表达式
  generateCronExpression(type, config) {
    try {
      let expression = '';
      
      switch (type) {
        case 'daily':
          // 每天指定时间
          expression = `${config.minute || 0} ${config.hour || 0} * * *`;
          break;
          
        case 'weekly':
          // 每周指定时间
          expression = `${config.minute || 0} ${config.hour || 0} * * ${config.dayOfWeek || 0}`;
          break;
          
        case 'monthly':
          // 每月指定时间
          expression = `${config.minute || 0} ${config.hour || 0} ${config.dayOfMonth || 1} * *`;
          break;
          
        case 'interval':
          // 间隔执行
          if (config.intervalType === 'minutes') {
            expression = `*/${config.interval} * * * *`;
          } else if (config.intervalType === 'hours') {
            expression = `0 */${config.interval} * * *`;
          }
          break;
          
        case 'custom':
          // 自定义表达式
          expression = config.expression || '0 0 * * *';
          break;
          
        default:
          throw new Error('不支持的Cron类型');
      }
      
      return {
        success: true,
        data: {
          expression,
          description: this.describeCronExpression(expression)
        }
      };
    } catch (error) {
      logger.script(`生成Cron表达式失败: ${error.message}`, 'error');
      throw error;
    }
  }
  
  // 解析Cron表达式
  parseCronExpression(expression) {
    try {
      const parts = expression.trim().split(/\s+/);
      
      if (parts.length !== 5) {
        throw new Error('Cron表达式格式错误，应为5个字段');
      }
      
      const [minute, hour, dayOfMonth, month, dayOfWeek] = parts;
      
      const parsed = {
        minute,
        hour,
        dayOfMonth,
        month,
        dayOfWeek,
        description: this.describeCronExpression(expression)
      };
      
      return {
        success: true,
        data: parsed
      };
    } catch (error) {
      logger.script(`解析Cron表达式失败: ${error.message}`, 'error');
      throw error;
    }
  }
  
  // 描述Cron表达式
  describeCronExpression(expression) {
    try {
      const parts = expression.trim().split(/\s+/);
      const [minute, hour, dayOfMonth, month, dayOfWeek] = parts;
      
      let description = '执行时间：';
      
      // 简单的描述逻辑
      if (minute === '*' && hour === '*') {
        description += '每分钟';
      } else if (hour === '*') {
        description += `每小时的第${minute}分钟`;
      } else if (dayOfMonth === '*' && month === '*' && dayOfWeek === '*') {
        description += `每天${hour}:${minute.padStart(2, '0')}`;
      } else {
        description += `${hour}:${minute.padStart(2, '0')}`;
        
        if (dayOfMonth !== '*') {
          description += `，每月${dayOfMonth}日`;
        }
        
        if (dayOfWeek !== '*') {
          const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
          description += `，${days[parseInt(dayOfWeek)] || '周' + dayOfWeek}`;
        }
      }
      
      return description;
    } catch (error) {
      return '无法解析表达式';
    }
  }
  
  // 导出脚本
  async exportScripts(filenames) {
    try {
      const archive = archiver('zip', {
        zlib: { level: 9 }
      });
      
      const buffers = [];
      
      archive.on('data', (chunk) => {
        buffers.push(chunk);
      });
      
      return new Promise((resolve, reject) => {
        archive.on('end', () => {
          const buffer = Buffer.concat(buffers);
          resolve({
            success: true,
            data: buffer
          });
        });
        
        archive.on('error', (error) => {
          reject(error);
        });
        
        // 添加文件到压缩包
        filenames.forEach(filename => {
          const filePath = path.join(this.scriptsDir, filename);
          archive.file(filePath, { name: filename });
        });
        
        archive.finalize();
      });
    } catch (error) {
      logger.script(`导出脚本失败: ${error.message}`, 'error');
      throw error;
    }
  }
  
  // 导入脚本
  async importScripts(files) {
    try {
      const results = [];
      
      for (const file of files) {
        try {
          const { filename, content } = file;
          
          if (!this.isValidScriptFile(filename)) {
            throw new Error('不支持的文件类型');
          }
          
          await this.saveLocalScript(filename, content, '导入的脚本');
          
          results.push({
            filename,
            success: true,
            message: '导入成功'
          });
        } catch (error) {
          results.push({
            filename: file.filename,
            success: false,
            message: error.message
          });
        }
      }
      
      const successCount = results.filter(r => r.success).length;
      const totalCount = results.length;
      
      return {
        success: true,
        message: `导入完成，成功${successCount}/${totalCount}个脚本`,
        data: results
      };
    } catch (error) {
      logger.script(`导入脚本失败: ${error.message}`, 'error');
      throw error;
    }
  }
  
  // 工具方法：检查是否为有效的脚本文件
  isValidScriptFile(filename) {
    const ext = path.extname(filename).toLowerCase();
    return config.script.allowedExtensions.includes(ext);
  }
  
  // 工具方法：获取脚本语言
  getScriptLanguage(filename) {
    const ext = path.extname(filename).toLowerCase();
    const languageMap = {
      '.js': 'javascript',
      '.py': 'python',
      '.sh': 'bash'
    };
    return languageMap[ext] || 'text';
  }
  
  // 工具方法：检查是否有版本历史
  async hasVersionHistory(filename) {
    try {
      const files = await fs.readdir(this.versionsDir);
      return files.some(file => file.startsWith(filename + '.'));
    } catch (error) {
      return false;
    }
  }
  
  // 工具方法：获取脚本元数据
  async getScriptMetadata(filename) {
    try {
      const filePath = path.join(this.scriptsDir, filename);
      const content = await fs.readFile(filePath, 'utf8');
      
      // 尝试从注释中提取元数据
      const metadata = {
        name: filename.replace(/\.[^/.]+$/, ''),
        description: ''
      };
      
      // 查找元数据注释
      const metadataRegex = /\/\*\s*METADATA\s*([\s\S]*?)\s*\*\//;
      const match = content.match(metadataRegex);
      
      if (match) {
        try {
          const metadataJson = JSON.parse(match[1]);
          Object.assign(metadata, metadataJson);
        } catch (error) {
          // 忽略JSON解析错误
        }
      }
      
      return metadata;
    } catch (error) {
      return {
        name: filename.replace(/\.[^/.]+$/, ''),
        description: ''
      };
    }
  }
  
  // 工具方法：为脚本添加元数据
  addMetadataToScript(content, metadata) {
    const metadataComment = `/*
METADATA
${JSON.stringify(metadata, null, 2)}
*/

`;
    
    // 如果已经有元数据注释，替换它
    const metadataRegex = /\/\*\s*METADATA\s*[\s\S]*?\*\/\s*/;
    if (metadataRegex.test(content)) {
      return content.replace(metadataRegex, metadataComment);
    } else {
      return metadataComment + content;
    }
  }
}

// 创建单例实例
const scriptService = new ScriptService();

module.exports = scriptService;
