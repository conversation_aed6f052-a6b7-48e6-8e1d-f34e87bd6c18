const express = require('express');
const router = express.Router();
const aiService = require('../services/aiService');
const logger = require('../utils/logger');

// 配置AI服务
router.post('/config', async (req, res) => {
  try {
    const { provider, apiKey, baseURL, model } = req.body;
    
    if (!provider || !apiKey) {
      return res.status(400).json({
        success: false,
        message: '请提供AI服务提供商和API密钥'
      });
    }
    
    aiService.configure(provider, apiKey, baseURL, model);
    
    res.json({
      success: true,
      message: 'AI服务配置成功'
    });
  } catch (error) {
    logger.error('配置AI服务失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 测试AI服务连接
router.post('/test', async (req, res) => {
  try {
    const { provider, apiKey, baseURL, model } = req.body;
    
    if (!provider || !apiKey) {
      return res.status(400).json({
        success: false,
        message: '请提供AI服务提供商和API密钥'
      });
    }
    
    // 临时配置用于测试
    aiService.configure(provider, apiKey, baseURL, model);
    const result = await aiService.testConnection();
    
    res.json(result);
  } catch (error) {
    logger.error('测试AI服务连接失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 生成脚本
router.post('/generate', async (req, res) => {
  try {
    const { prompt, template, language = 'javascript' } = req.body;
    
    if (!prompt) {
      return res.status(400).json({
        success: false,
        message: '请提供生成提示'
      });
    }
    
    const result = await aiService.generateScript(prompt, template, language);
    res.json(result);
  } catch (error) {
    logger.error('生成脚本失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 优化脚本
router.post('/optimize', async (req, res) => {
  try {
    const { code, requirements } = req.body;
    
    if (!code) {
      return res.status(400).json({
        success: false,
        message: '请提供要优化的代码'
      });
    }
    
    const result = await aiService.optimizeScript(code, requirements);
    res.json(result);
  } catch (error) {
    logger.error('优化脚本失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 解释代码
router.post('/explain', async (req, res) => {
  try {
    const { code } = req.body;
    
    if (!code) {
      return res.status(400).json({
        success: false,
        message: '请提供要解释的代码'
      });
    }
    
    const result = await aiService.explainCode(code);
    res.json(result);
  } catch (error) {
    logger.error('解释代码失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 获取脚本模板
router.get('/templates', async (req, res) => {
  try {
    const templates = aiService.getTemplates();
    res.json({
      success: true,
      data: templates
    });
  } catch (error) {
    logger.error('获取脚本模板失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 获取生成历史
router.get('/history', async (req, res) => {
  try {
    const { page = 1, pageSize = 20 } = req.query;
    const result = await aiService.getGenerationHistory(parseInt(page), parseInt(pageSize));
    res.json(result);
  } catch (error) {
    logger.error('获取生成历史失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 保存生成记录
router.post('/history', async (req, res) => {
  try {
    const { prompt, code, template, isFavorite = false } = req.body;
    
    if (!prompt || !code) {
      return res.status(400).json({
        success: false,
        message: '请提供提示和生成的代码'
      });
    }
    
    const result = await aiService.saveGenerationRecord({
      prompt,
      code,
      template,
      isFavorite
    });
    
    res.json(result);
  } catch (error) {
    logger.error('保存生成记录失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 删除生成记录
router.delete('/history/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await aiService.deleteGenerationRecord(id);
    res.json(result);
  } catch (error) {
    logger.error('删除生成记录失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 收藏/取消收藏生成记录
router.put('/history/:id/favorite', async (req, res) => {
  try {
    const { id } = req.params;
    const { isFavorite } = req.body;
    
    const result = await aiService.toggleFavorite(id, isFavorite);
    res.json(result);
  } catch (error) {
    logger.error('更新收藏状态失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 语音转文字
router.post('/speech-to-text', async (req, res) => {
  try {
    const { audioData } = req.body;
    
    if (!audioData) {
      return res.status(400).json({
        success: false,
        message: '请提供音频数据'
      });
    }
    
    const result = await aiService.speechToText(audioData);
    res.json(result);
  } catch (error) {
    logger.error('语音转文字失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 代码语法检查
router.post('/validate', async (req, res) => {
  try {
    const { code, language = 'javascript' } = req.body;
    
    if (!code) {
      return res.status(400).json({
        success: false,
        message: '请提供要检查的代码'
      });
    }
    
    const result = await aiService.validateCode(code, language);
    res.json(result);
  } catch (error) {
    logger.error('代码语法检查失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

module.exports = router;
