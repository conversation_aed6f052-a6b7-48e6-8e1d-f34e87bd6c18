import request from './request'

export const logApi = {
  // 内存日志
  getMemoryLogs(params) {
    return request.get('/log/memory', { 
      params,
      showLoading: false // 日志查询不显示loading
    })
  },
  
  clearMemoryLogs() {
    return request.delete('/log/memory')
  },
  
  // 导出日志
  exportLogs(data) {
    return request.post('/log/export', data, {
      responseType: 'blob'
    })
  },
  
  // 日志统计
  getLogStats() {
    return request.get('/log/stats', {
      showLoading: false
    })
  },
  
  // 添加自定义日志
  addLog(data) {
    return request.post('/log/add', data)
  },
  
  // 系统日志文件
  getLogFiles() {
    return request.get('/log/files')
  },
  
  downloadLogFile(filename) {
    return request.get(`/log/files/${filename}`, {
      responseType: 'blob'
    })
  }
}
