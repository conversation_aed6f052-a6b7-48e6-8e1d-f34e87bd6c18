// 重置样式
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--el-bg-color-page);
  color: var(--el-text-color-primary);
  transition: background-color var(--transition-duration) var(--transition-function),
              color var(--transition-duration) var(--transition-function);
}

// 主题切换动画
* {
  transition: background-color var(--transition-duration) var(--transition-function),
              border-color var(--transition-duration) var(--transition-function),
              color var(--transition-duration) var(--transition-function),
              box-shadow var(--transition-duration) var(--transition-function);
}

#app {
  height: 100%;
}

// 通用工具类
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.full-width {
  width: 100%;
}

.full-height {
  height: 100%;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

.cursor-pointer {
  cursor: pointer;
}

.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

// 间距工具类
@for $i from 0 through 10 {
  .m-#{$i} { margin: #{$i * 4}px; }
  .mt-#{$i} { margin-top: #{$i * 4}px; }
  .mr-#{$i} { margin-right: #{$i * 4}px; }
  .mb-#{$i} { margin-bottom: #{$i * 4}px; }
  .ml-#{$i} { margin-left: #{$i * 4}px; }
  .mx-#{$i} { margin-left: #{$i * 4}px; margin-right: #{$i * 4}px; }
  .my-#{$i} { margin-top: #{$i * 4}px; margin-bottom: #{$i * 4}px; }
  
  .p-#{$i} { padding: #{$i * 4}px; }
  .pt-#{$i} { padding-top: #{$i * 4}px; }
  .pr-#{$i} { padding-right: #{$i * 4}px; }
  .pb-#{$i} { padding-bottom: #{$i * 4}px; }
  .pl-#{$i} { padding-left: #{$i * 4}px; }
  .px-#{$i} { padding-left: #{$i * 4}px; padding-right: #{$i * 4}px; }
  .py-#{$i} { padding-top: #{$i * 4}px; padding-bottom: #{$i * 4}px; }
}

// 文字大小工具类
.text-xs { font-size: 12px; }
.text-sm { font-size: 14px; }
.text-base { font-size: 16px; }
.text-lg { font-size: 18px; }
.text-xl { font-size: 20px; }
.text-2xl { font-size: 24px; }
.text-3xl { font-size: 30px; }

// 字重工具类
.font-thin { font-weight: 100; }
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.font-black { font-weight: 900; }

// 颜色工具类
.text-primary { color: var(--el-color-primary); }
.text-success { color: var(--el-color-success); }
.text-warning { color: var(--el-color-warning); }
.text-danger { color: var(--el-color-danger); }
.text-info { color: var(--el-color-info); }

.bg-primary { background-color: var(--el-color-primary); }
.bg-success { background-color: var(--el-color-success); }
.bg-warning { background-color: var(--el-color-warning); }
.bg-danger { background-color: var(--el-color-danger); }
.bg-info { background-color: var(--el-color-info); }

// 日志级别样式
.log-level {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  
  &.error {
    background-color: var(--el-color-danger-light-9);
    color: var(--el-color-danger);
    border: 1px solid var(--el-color-danger-light-7);
  }
  
  &.warn {
    background-color: var(--el-color-warning-light-9);
    color: var(--el-color-warning);
    border: 1px solid var(--el-color-warning-light-7);
  }
  
  &.info {
    background-color: var(--el-color-primary-light-9);
    color: var(--el-color-primary);
    border: 1px solid var(--el-color-primary-light-7);
  }
  
  &.success {
    background-color: var(--el-color-success-light-9);
    color: var(--el-color-success);
    border: 1px solid var(--el-color-success-light-7);
  }
}

// 代码块样式
.code-block {
  background-color: var(--el-fill-color-light);
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  overflow-x: auto;
  
  &.dark {
    background-color: #1e1e1e;
    color: #d4d4d4;
    border-color: #3e3e3e;
  }
}

// 卡片样式增强
.el-card {
  border-radius: 12px;
  box-shadow: var(--el-box-shadow-lighter);
  transition: all var(--transition-duration) var(--transition-function);
  border: 1px solid var(--el-border-color-lighter);
  overflow: hidden;

  &:hover {
    box-shadow: var(--el-box-shadow-light);
    transform: translateY(-2px);
  }
  
  .el-card__header {
    border-bottom: 1px solid var(--el-border-color-lighter);
    padding: 16px 20px;
    
    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .title {
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }
  }
  
  .el-card__body {
    padding: 20px;
  }
}

// 表格样式增强
.el-table {
  .el-table__header {
    th {
      background-color: var(--el-fill-color-light);
      color: var(--el-text-color-primary);
      font-weight: 600;
    }
  }
  
  .el-table__row {
    &:hover {
      background-color: var(--el-fill-color-lighter);
    }
  }
}

// 按钮样式增强
.el-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all var(--transition-duration) var(--transition-function);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover::before {
    left: 100%;
  }

  &.is-circle {
    border-radius: 50%;
  }

  &.is-round {
    border-radius: 20px;
  }

  // 按钮类型特殊效果
  &--primary {
    background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));

    &:hover {
      background: linear-gradient(135deg, var(--el-color-primary-dark-2), var(--el-color-primary));
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
    }
  }

  &--success {
    background: linear-gradient(135deg, var(--el-color-success), var(--el-color-success-light-3));

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
    }
  }

  &--warning {
    background: linear-gradient(135deg, var(--el-color-warning), var(--el-color-warning-light-3));

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(230, 162, 60, 0.3);
    }
  }

  &--danger {
    background: linear-gradient(135deg, var(--el-color-danger), var(--el-color-danger-light-3));

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
    }
  }
}

// 输入框样式增强
.el-input {
  .el-input__wrapper {
    border-radius: 6px;
    transition: all var(--transition-duration) var(--transition-function);
  }
}

.el-textarea {
  .el-textarea__inner {
    border-radius: 6px;
    transition: all var(--transition-duration) var(--transition-function);
  }
}

// 选择器样式增强
.el-select {
  .el-input {
    .el-input__wrapper {
      border-radius: 6px;
    }
  }
}

// 对话框样式增强
.el-dialog {
  border-radius: 12px;
  
  .el-dialog__header {
    padding: 20px 20px 10px;
    
    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
    }
  }
  
  .el-dialog__body {
    padding: 10px 20px 20px;
  }
  
  .el-dialog__footer {
    padding: 10px 20px 20px;
  }
}

// 抽屉样式增强
.el-drawer {
  .el-drawer__header {
    padding: 20px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    
    .el-drawer__title {
      font-size: 18px;
      font-weight: 600;
    }
  }
  
  .el-drawer__body {
    padding: 20px;
  }
}

// 标签页样式增强
.el-tabs {
  .el-tabs__header {
    margin-bottom: 20px;
    
    .el-tabs__nav {
      border-radius: 6px;
    }
    
    .el-tabs__item {
      font-weight: 500;
      transition: all var(--transition-duration) var(--transition-function);
      
      &.is-active {
        font-weight: 600;
      }
    }
  }
}

// 消息提示样式增强
.el-message {
  border-radius: 8px;
  box-shadow: var(--el-box-shadow);
}

.el-notification {
  border-radius: 8px;
  box-shadow: var(--el-box-shadow);
}

// 加载样式
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: var(--el-text-color-secondary);

  .loading-text {
    margin-top: 16px;
    font-size: 14px;
    animation: pulse 2s infinite;
  }
}

// 脉冲动画
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 淡入动画
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 滑入动画
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 弹跳动画
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -30px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0,-4px,0);
  }
}

// 动画工具类
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

.bounce {
  animation: bounce 1s;
}

// 空状态样式
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--el-text-color-secondary);
  
  .empty-icon {
    font-size: 64px;
    margin-bottom: 16px;
    opacity: 0.5;
  }
  
  .empty-text {
    font-size: 16px;
    margin-bottom: 8px;
  }
  
  .empty-description {
    font-size: 14px;
    opacity: 0.8;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .el-card {
    .el-card__header {
      padding: 12px 16px;
    }
    
    .el-card__body {
      padding: 16px;
    }
  }
  
  .el-dialog {
    width: 90% !important;
    margin: 5vh auto !important;
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--el-border-color-dark);
  border-radius: 4px;
  
  &:hover {
    background: var(--el-border-color-darker);
  }
}

// Firefox 滚动条
* {
  scrollbar-width: thin;
  scrollbar-color: var(--el-border-color-dark) var(--el-fill-color-lighter);
}
