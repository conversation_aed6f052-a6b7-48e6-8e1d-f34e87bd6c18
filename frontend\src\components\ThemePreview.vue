<template>
  <div class="theme-preview">
    <div class="preview-container">
      <div class="preview-header">
        <div class="preview-title">主题预览</div>
        <div class="preview-actions">
          <el-button
            v-for="theme in themes"
            :key="theme.key"
            :type="currentTheme === theme.key ? 'primary' : 'default'"
            size="small"
            @click="switchTheme(theme.key)"
          >
            {{ theme.name }}
          </el-button>
        </div>
      </div>
      
      <div class="preview-content">
        <div class="preview-card">
          <div class="card-header">
            <span class="card-title">示例卡片</span>
            <el-tag type="success" size="small">成功</el-tag>
          </div>
          <div class="card-body">
            <p>这是一个示例卡片，展示当前主题的效果。</p>
            <div class="button-group">
              <el-button type="primary" size="small">主要按钮</el-button>
              <el-button type="success" size="small">成功按钮</el-button>
              <el-button type="warning" size="small">警告按钮</el-button>
              <el-button type="danger" size="small">危险按钮</el-button>
            </div>
          </div>
        </div>
        
        <div class="preview-form">
          <el-form label-width="80px" size="small">
            <el-form-item label="输入框">
              <el-input v-model="sampleText" placeholder="请输入内容" />
            </el-form-item>
            <el-form-item label="选择器">
              <el-select v-model="sampleSelect" placeholder="请选择">
                <el-option label="选项1" value="1" />
                <el-option label="选项2" value="2" />
              </el-select>
            </el-form-item>
            <el-form-item label="开关">
              <el-switch v-model="sampleSwitch" />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useThemeStore } from '@/stores/theme'

const themeStore = useThemeStore()

// 响应式数据
const sampleText = ref('示例文本')
const sampleSelect = ref('')
const sampleSwitch = ref(false)

const themes = [
  { key: 'light', name: '浅色主题' },
  { key: 'dark', name: '深色主题' },
  { key: 'auto', name: '跟随系统' }
]

const currentTheme = computed(() => {
  if (themeStore.isDark) return 'dark'
  return 'light'
})

// 方法
const switchTheme = (themeKey) => {
  if (themeKey === 'auto') {
    // 跟随系统主题
    const systemDark = window.matchMedia('(prefers-color-scheme: dark)').matches
    if (systemDark !== themeStore.isDark) {
      themeStore.toggleTheme()
    }
  } else if (themeKey === 'dark' && !themeStore.isDark) {
    themeStore.toggleTheme()
  } else if (themeKey === 'light' && themeStore.isDark) {
    themeStore.toggleTheme()
  }
}
</script>

<style lang="scss" scoped>
.theme-preview {
  .preview-container {
    border: 1px solid var(--el-border-color);
    border-radius: 8px;
    overflow: hidden;
    background: var(--el-bg-color);
    
    .preview-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 20px;
      background: var(--el-fill-color-light);
      border-bottom: 1px solid var(--el-border-color);
      
      .preview-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
      
      .preview-actions {
        display: flex;
        gap: 8px;
      }
    }
    
    .preview-content {
      padding: 20px;
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      
      .preview-card {
        border: 1px solid var(--el-border-color-lighter);
        border-radius: 6px;
        overflow: hidden;
        
        .card-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 12px 16px;
          background: var(--el-fill-color-lighter);
          border-bottom: 1px solid var(--el-border-color-lighter);
          
          .card-title {
            font-weight: 500;
            color: var(--el-text-color-primary);
          }
        }
        
        .card-body {
          padding: 16px;
          
          p {
            margin: 0 0 16px 0;
            color: var(--el-text-color-regular);
            line-height: 1.5;
          }
          
          .button-group {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
          }
        }
      }
      
      .preview-form {
        .el-form-item {
          margin-bottom: 16px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .theme-preview {
    .preview-container {
      .preview-header {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
      }
      
      .preview-content {
        grid-template-columns: 1fr;
      }
    }
  }
}
</style>
