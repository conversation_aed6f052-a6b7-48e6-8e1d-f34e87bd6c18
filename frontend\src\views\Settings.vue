<template>
  <div class="settings">
    <el-row :gutter="20">
      <el-col :xs="24" :lg="12">
        <!-- 青龙面板连接设置 -->
        <el-card>
          <template #header>
            <div class="card-header">
              <span class="title">青龙面板连接设置</span>
              <el-tag
                :type="qinglongStore.isConnected ? 'success' : 'danger'"
                size="small"
              >
                {{ qinglongStore.isConnected ? '已连接' : '未连接' }}
              </el-tag>
            </div>
          </template>
          
          <el-form
            ref="qinglongFormRef"
            :model="qinglongForm"
            :rules="qinglongRules"
            label-width="120px"
            @submit.prevent
          >
            <el-form-item label="面板地址" prop="baseURL">
              <el-input
                v-model="qinglongForm.baseURL"
                placeholder="http://*************:5700"
                clearable
              >
                <template #prepend>
                  <el-icon><Link /></el-icon>
                </template>
              </el-input>
              <div class="form-tip">
                请输入青龙面板的完整地址，包括协议和端口
              </div>
            </el-form-item>
            
            <el-form-item label="Client ID" prop="clientId">
              <el-input
                v-model="qinglongForm.clientId"
                placeholder="请输入Client ID"
                clearable
              >
                <template #prepend>
                  <el-icon><User /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            
            <el-form-item label="Client Secret" prop="clientSecret">
              <el-input
                v-model="qinglongForm.clientSecret"
                type="password"
                placeholder="请输入Client Secret"
                show-password
                clearable
              >
                <template #prepend>
                  <el-icon><Key /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            
            <el-form-item>
              <el-button
                type="primary"
                @click="testQinglongConnection"
                :loading="testingQinglong"
              >
                测试连接
              </el-button>
              <el-button
                type="success"
                @click="saveQinglongConfig"
                :loading="savingQinglong"
              >
                保存配置
              </el-button>
            </el-form-item>
          </el-form>
          
          <el-divider />
          
          <div class="config-help">
            <h4>如何获取Client ID和Client Secret？</h4>
            <ol>
              <li>登录青龙面板</li>
              <li>进入"系统设置" → "应用设置"</li>
              <li>点击"新建应用"或使用现有应用</li>
              <li>复制Client ID和Client Secret</li>
            </ol>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :lg="12">
        <!-- AI服务配置 -->
        <el-card>
          <template #header>
            <div class="card-header">
              <span class="title">AI服务配置</span>
              <el-tag type="info" size="small">可选</el-tag>
            </div>
          </template>
          
          <el-form
            ref="aiFormRef"
            :model="aiForm"
            :rules="aiRules"
            label-width="120px"
            @submit.prevent
          >
            <el-form-item label="AI提供商" prop="provider">
              <el-select
                v-model="aiForm.provider"
                placeholder="请选择AI提供商"
                style="width: 100%"
              >
                <el-option
                  label="OpenAI GPT-4"
                  value="openai"
                />
                <el-option
                  label="Anthropic Claude"
                  value="claude"
                />
                <el-option
                  label="本地Ollama"
                  value="ollama"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="API密钥" prop="apiKey" v-if="aiForm.provider !== 'ollama'">
              <el-input
                v-model="aiForm.apiKey"
                type="password"
                placeholder="请输入API密钥"
                show-password
                clearable
              >
                <template #prepend>
                  <el-icon><Key /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            
            <el-form-item label="API地址" prop="baseURL">
              <el-input
                v-model="aiForm.baseURL"
                placeholder="自定义API地址（可选）"
                clearable
              >
                <template #prepend>
                  <el-icon><Link /></el-icon>
                </template>
              </el-input>
              <div class="form-tip">
                留空使用默认地址
              </div>
            </el-form-item>
            
            <el-form-item label="模型" prop="model">
              <el-input
                v-model="aiForm.model"
                placeholder="自定义模型名称（可选）"
                clearable
              >
                <template #prepend>
                  <el-icon><Cpu /></el-icon>
                </template>
              </el-input>
              <div class="form-tip">
                留空使用默认模型
              </div>
            </el-form-item>
            
            <el-form-item>
              <el-button
                type="primary"
                @click="testAIConnection"
                :loading="testingAI"
                :disabled="!aiForm.provider || (aiForm.provider !== 'ollama' && !aiForm.apiKey)"
              >
                测试连接
              </el-button>
              <el-button
                type="success"
                @click="saveAIConfig"
                :loading="savingAI"
              >
                保存配置
              </el-button>
            </el-form-item>
          </el-form>
          
          <el-divider />
          
          <div class="config-help">
            <h4>AI服务说明</h4>
            <ul>
              <li><strong>OpenAI:</strong> 需要API密钥，支持GPT-4等模型</li>
              <li><strong>Claude:</strong> 需要API密钥，支持Claude-3等模型</li>
              <li><strong>Ollama:</strong> 本地部署，无需API密钥</li>
            </ul>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 系统信息 -->
    <el-row :gutter="20" class="mt-4">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span class="title">系统信息</span>
              <el-button
                type="primary"
                size="small"
                text
                @click="refreshSystemInfo"
                :loading="refreshingInfo"
              >
                刷新
              </el-button>
            </div>
          </template>
          
          <el-descriptions :column="3" border>
            <el-descriptions-item label="应用版本">
              v1.0.0
            </el-descriptions-item>
            <el-descriptions-item label="运行环境">
              {{ systemInfo.env }}
            </el-descriptions-item>
            <el-descriptions-item label="启动时间">
              {{ systemInfo.startTime }}
            </el-descriptions-item>
            <el-descriptions-item label="青龙面板状态">
              <el-tag :type="qinglongStore.isConnected ? 'success' : 'danger'">
                {{ qinglongStore.isConnected ? '已连接' : '未连接' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="AI服务状态">
              <el-tag type="info">
                {{ aiConnected ? '已连接' : '未配置' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="WebSocket状态">
              <el-tag :type="socketConnected ? 'success' : 'danger'">
                {{ socketConnected ? '已连接' : '未连接' }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useQinglongStore } from '@/stores/qinglong'
import { qinglongApi } from '@/api/qinglong'
import { aiApi } from '@/api/ai'
import {
  Link,
  User,
  Key,
  Cpu
} from '@element-plus/icons-vue'

const qinglongStore = useQinglongStore()

// 表单引用
const qinglongFormRef = ref()
const aiFormRef = ref()

// 青龙面板配置表单
const qinglongForm = reactive({
  baseURL: '',
  clientId: '',
  clientSecret: ''
})

const qinglongRules = {
  baseURL: [
    { required: true, message: '请输入面板地址', trigger: 'blur' },
    { pattern: /^https?:\/\/.+/, message: '请输入有效的URL地址', trigger: 'blur' }
  ],
  clientId: [
    { required: true, message: '请输入Client ID', trigger: 'blur' }
  ],
  clientSecret: [
    { required: true, message: '请输入Client Secret', trigger: 'blur' }
  ]
}

// AI服务配置表单
const aiForm = reactive({
  provider: '',
  apiKey: '',
  baseURL: '',
  model: ''
})

const aiRules = {
  provider: [
    { required: true, message: '请选择AI提供商', trigger: 'change' }
  ],
  apiKey: [
    { 
      required: true, 
      message: '请输入API密钥', 
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (aiForm.provider === 'ollama') {
          callback()
        } else if (!value) {
          callback(new Error('请输入API密钥'))
        } else {
          callback()
        }
      }
    }
  ]
}

// 状态变量
const testingQinglong = ref(false)
const savingQinglong = ref(false)
const testingAI = ref(false)
const savingAI = ref(false)
const refreshingInfo = ref(false)
const aiConnected = ref(false)
const socketConnected = ref(false)

const systemInfo = reactive({
  env: 'development',
  startTime: new Date().toLocaleString()
})

// 方法
const testQinglongConnection = async () => {
  try {
    await qinglongFormRef.value.validate()
    
    testingQinglong.value = true
    const response = await qinglongApi.testConnection(qinglongForm)
    
    if (response.success) {
      ElMessage.success('连接测试成功')
      qinglongStore.isConnected = true
      qinglongStore.systemInfo = response.data
    }
  } catch (error) {
    console.error('测试连接失败:', error)
  } finally {
    testingQinglong.value = false
  }
}

const saveQinglongConfig = async () => {
  try {
    await qinglongFormRef.value.validate()
    
    savingQinglong.value = true
    
    // 保存到store
    qinglongStore.setConnectionInfo(qinglongForm)
    
    // 配置后端连接
    await qinglongApi.configConnection(qinglongForm)
    
    ElMessage.success('配置保存成功')
  } catch (error) {
    console.error('保存配置失败:', error)
  } finally {
    savingQinglong.value = false
  }
}

const testAIConnection = async () => {
  try {
    await aiFormRef.value.validate()
    
    testingAI.value = true
    const response = await aiApi.testAI(aiForm)
    
    if (response.success) {
      ElMessage.success('AI服务连接测试成功')
      aiConnected.value = true
    }
  } catch (error) {
    console.error('AI服务测试失败:', error)
  } finally {
    testingAI.value = false
  }
}

const saveAIConfig = async () => {
  try {
    if (aiForm.provider) {
      await aiFormRef.value.validate()
    }
    
    savingAI.value = true
    
    // 保存AI配置到本地存储
    localStorage.setItem('ai_config', JSON.stringify(aiForm))
    
    // 配置后端AI服务
    if (aiForm.provider && (aiForm.provider === 'ollama' || aiForm.apiKey)) {
      await aiApi.configAI(aiForm)
    }
    
    ElMessage.success('AI配置保存成功')
  } catch (error) {
    console.error('保存AI配置失败:', error)
  } finally {
    savingAI.value = false
  }
}

const refreshSystemInfo = async () => {
  try {
    refreshingInfo.value = true
    
    // 刷新青龙面板连接状态
    if (qinglongStore.isConfigured) {
      await qinglongStore.testConnection()
    }
    
    // 检查AI服务状态
    if (aiForm.provider && (aiForm.provider === 'ollama' || aiForm.apiKey)) {
      try {
        const response = await aiApi.testAI(aiForm)
        aiConnected.value = response.success
      } catch (error) {
        aiConnected.value = false
      }
    }
    
    ElMessage.success('系统信息已刷新')
  } catch (error) {
    console.error('刷新系统信息失败:', error)
  } finally {
    refreshingInfo.value = false
  }
}

const loadSavedConfigs = () => {
  // 加载青龙面板配置
  if (qinglongStore.connectionInfo.baseURL) {
    Object.assign(qinglongForm, qinglongStore.connectionInfo)
  }
  
  // 加载AI配置
  const savedAIConfig = localStorage.getItem('ai_config')
  if (savedAIConfig) {
    try {
      Object.assign(aiForm, JSON.parse(savedAIConfig))
    } catch (error) {
      console.error('加载AI配置失败:', error)
    }
  }
}

// 生命周期
onMounted(() => {
  loadSavedConfigs()
})
</script>

<style lang="scss" scoped>
.settings {
  .form-tip {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-top: 4px;
  }
  
  .config-help {
    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      color: var(--el-text-color-primary);
    }
    
    ol, ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin: 4px 0;
        font-size: 13px;
        color: var(--el-text-color-regular);
        line-height: 1.5;
      }
    }
  }
}
</style>
