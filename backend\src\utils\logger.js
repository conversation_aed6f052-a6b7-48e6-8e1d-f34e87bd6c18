const winston = require('winston');
const path = require('path');
const config = require('../config/config');

// 自定义日志格式
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.printf(({ level, message, timestamp, stack }) => {
    return `${timestamp} [${level.toUpperCase()}]: ${stack || message}`;
  })
);

// 创建logger实例
const logger = winston.createLogger({
  level: config.logging.level,
  format: logFormat,
  transports: [
    // 控制台输出
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        logFormat
      )
    }),
    
    // 错误日志文件
    new winston.transports.File({
      filename: path.join(__dirname, '../../logs/error.log'),
      level: 'error',
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5,
      tailable: true
    }),
    
    // 所有日志文件
    new winston.transports.File({
      filename: path.join(__dirname, '../../logs/combined.log'),
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 10,
      tailable: true
    })
  ],
  
  // 异常处理
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(__dirname, '../../logs/exceptions.log')
    })
  ],
  
  // 拒绝处理
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(__dirname, '../../logs/rejections.log')
    })
  ]
});

// 生产环境不输出到控制台
if (process.env.NODE_ENV === 'production') {
  logger.remove(winston.transports.Console);
}

// 内存中的日志存储（用于实时推送）
class MemoryLogStore {
  constructor(maxSize = 1000) {
    this.logs = [];
    this.maxSize = maxSize;
  }
  
  add(logEntry) {
    this.logs.push({
      id: Date.now() + Math.random(),
      timestamp: new Date().toISOString(),
      level: logEntry.level,
      message: logEntry.message,
      source: logEntry.source || 'system'
    });
    
    // 保持最大条数限制
    if (this.logs.length > this.maxSize) {
      this.logs = this.logs.slice(-this.maxSize);
    }
  }
  
  getLogs(filter = {}) {
    let filteredLogs = [...this.logs];
    
    // 按级别过滤
    if (filter.level) {
      filteredLogs = filteredLogs.filter(log => log.level === filter.level);
    }
    
    // 按关键词过滤
    if (filter.keyword) {
      const keyword = filter.keyword.toLowerCase();
      filteredLogs = filteredLogs.filter(log => 
        log.message.toLowerCase().includes(keyword)
      );
    }
    
    // 按时间范围过滤
    if (filter.startTime) {
      filteredLogs = filteredLogs.filter(log => 
        new Date(log.timestamp) >= new Date(filter.startTime)
      );
    }
    
    if (filter.endTime) {
      filteredLogs = filteredLogs.filter(log => 
        new Date(log.timestamp) <= new Date(filter.endTime)
      );
    }
    
    // 分页
    const page = filter.page || 1;
    const pageSize = filter.pageSize || 50;
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    
    return {
      logs: filteredLogs.slice(startIndex, endIndex),
      total: filteredLogs.length,
      page,
      pageSize,
      totalPages: Math.ceil(filteredLogs.length / pageSize)
    };
  }
  
  clear() {
    this.logs = [];
  }
}

// 创建内存日志存储实例
const memoryLogStore = new MemoryLogStore(config.logging.maxLogEntries);

// 扩展logger，添加内存存储功能
const originalLog = logger.log;
logger.log = function(level, message, meta = {}) {
  // 调用原始log方法
  originalLog.call(this, level, message, meta);

  // 添加到内存存储
  const logEntry = {
    level: typeof level === 'object' ? level.level : level,
    message: typeof level === 'object' ? level.message : message,
    source: meta.source
  };

  memoryLogStore.add(logEntry);

  // 通过WebSocket推送新日志（如果有io实例）
  if (global.io) {
    global.io.to('logs').emit('new-log', {
      id: Date.now() + Math.random(),
      timestamp: new Date().toISOString(),
      level: logEntry.level,
      message: logEntry.message,
      source: logEntry.source || 'system'
    });
  }
};

// 添加便捷方法
logger.getMemoryLogs = (filter) => memoryLogStore.getLogs(filter);
logger.clearMemoryLogs = () => memoryLogStore.clear();

// 添加青龙面板日志方法
logger.qinglong = (message, level = 'info') => {
  logger.log(level, message, { source: 'qinglong' });
};

// 添加AI服务日志方法
logger.ai = (message, level = 'info') => {
  logger.log(level, message, { source: 'ai' });
};

// 添加脚本日志方法
logger.script = (message, level = 'info') => {
  logger.log(level, message, { source: 'script' });
};

module.exports = logger;
