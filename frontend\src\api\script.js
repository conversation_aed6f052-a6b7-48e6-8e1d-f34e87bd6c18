import request from './request'

export const scriptApi = {
  // 本地脚本管理
  getLocalScripts() {
    return request.get('/script/local')
  },
  
  saveLocalScript(data) {
    return request.post('/script/local', data)
  },
  
  getLocalScriptContent(filename) {
    return request.get(`/script/local/${filename}`)
  },
  
  deleteLocalScript(filename) {
    return request.delete(`/script/local/${filename}`)
  },
  
  // 版本管理
  getScriptVersions(filename) {
    return request.get(`/script/versions/${filename}`)
  },
  
  rollbackScript(data) {
    return request.post('/script/rollback', data)
  },
  
  // 批量操作
  batchDeploy(data) {
    return request.post('/script/batch-deploy', data)
  },
  
  // 依赖检查
  checkDependencies(data) {
    return request.post('/script/check-dependencies', data)
  },
  
  // Cron表达式
  generateCron(data) {
    return request.post('/script/generate-cron', data)
  },
  
  parseCron(data) {
    return request.post('/script/parse-cron', data)
  },
  
  // 导入导出
  exportScripts(data) {
    return request.post('/script/export', data, {
      responseType: 'blob'
    })
  },
  
  importScripts(data) {
    return request.post('/script/import', data)
  }
}
