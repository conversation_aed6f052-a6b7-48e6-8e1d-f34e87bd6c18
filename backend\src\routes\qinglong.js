const express = require('express');
const router = express.Router();
const qinglongService = require('../services/qinglongService');
const logger = require('../utils/logger');

// 配置青龙面板连接
router.post('/config', async (req, res) => {
  try {
    const { baseURL, clientId, clientSecret } = req.body;
    
    if (!baseURL || !clientId || !clientSecret) {
      return res.status(400).json({
        success: false,
        message: '请提供完整的连接信息'
      });
    }
    
    qinglongService.configure(baseURL, clientId, clientSecret);
    
    res.json({
      success: true,
      message: '连接配置成功'
    });
  } catch (error) {
    logger.error('配置青龙面板连接失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 测试连接
router.post('/test', async (req, res) => {
  try {
    const { baseURL, clientId, clientSecret } = req.body;
    
    if (!baseURL || !clientId || !clientSecret) {
      return res.status(400).json({
        success: false,
        message: '请提供完整的连接信息'
      });
    }
    
    // 临时配置用于测试
    qinglongService.configure(baseURL, clientId, clientSecret);
    const result = await qinglongService.testConnection();
    
    res.json(result);
  } catch (error) {
    logger.error('测试连接失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 获取脚本列表
router.get('/scripts', async (req, res) => {
  try {
    const result = await qinglongService.getScripts();
    res.json(result);
  } catch (error) {
    logger.error('获取脚本列表失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 获取脚本内容
router.get('/scripts/:filename', async (req, res) => {
  try {
    const { filename } = req.params;
    const result = await qinglongService.getScriptContent(filename);
    res.json(result);
  } catch (error) {
    logger.error('获取脚本内容失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 上传脚本
router.post('/scripts', async (req, res) => {
  try {
    const { filename, content } = req.body;
    
    if (!filename || !content) {
      return res.status(400).json({
        success: false,
        message: '请提供文件名和内容'
      });
    }
    
    const result = await qinglongService.uploadScript(filename, content);
    res.json(result);
  } catch (error) {
    logger.error('上传脚本失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 删除脚本
router.delete('/scripts/:filename', async (req, res) => {
  try {
    const { filename } = req.params;
    const result = await qinglongService.deleteScript(filename);
    res.json(result);
  } catch (error) {
    logger.error('删除脚本失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 获取定时任务列表
router.get('/crons', async (req, res) => {
  try {
    const result = await qinglongService.getCrons();
    res.json(result);
  } catch (error) {
    logger.error('获取定时任务列表失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 添加定时任务
router.post('/crons', async (req, res) => {
  try {
    const cronData = req.body;
    
    if (!cronData.name || !cronData.command || !cronData.schedule) {
      return res.status(400).json({
        success: false,
        message: '请提供完整的任务信息'
      });
    }
    
    const result = await qinglongService.addCron(cronData);
    res.json(result);
  } catch (error) {
    logger.error('添加定时任务失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 更新定时任务
router.put('/crons', async (req, res) => {
  try {
    const cronData = req.body;
    
    if (!cronData.id) {
      return res.status(400).json({
        success: false,
        message: '请提供任务ID'
      });
    }
    
    const result = await qinglongService.updateCron(cronData);
    res.json(result);
  } catch (error) {
    logger.error('更新定时任务失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 删除定时任务
router.delete('/crons', async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供要删除的任务ID列表'
      });
    }
    
    const result = await qinglongService.deleteCron(ids);
    res.json(result);
  } catch (error) {
    logger.error('删除定时任务失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 启用定时任务
router.put('/crons/enable', async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供要启用的任务ID列表'
      });
    }
    
    const result = await qinglongService.toggleCron(ids, true);
    res.json(result);
  } catch (error) {
    logger.error('启用定时任务失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 禁用定时任务
router.put('/crons/disable', async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供要禁用的任务ID列表'
      });
    }
    
    const result = await qinglongService.toggleCron(ids, false);
    res.json(result);
  } catch (error) {
    logger.error('禁用定时任务失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 运行定时任务
router.put('/crons/run', async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供要运行的任务ID列表'
      });
    }
    
    const result = await qinglongService.runCron(ids);
    res.json(result);
  } catch (error) {
    logger.error('运行定时任务失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 获取日志
router.get('/logs/:filename', async (req, res) => {
  try {
    const { filename } = req.params;
    const result = await qinglongService.getLogs(filename);
    res.json(result);
  } catch (error) {
    logger.error('获取日志失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 获取环境变量列表
router.get('/envs', async (req, res) => {
  try {
    const result = await qinglongService.getEnvs();
    res.json(result);
  } catch (error) {
    logger.error('获取环境变量列表失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 添加环境变量
router.post('/envs', async (req, res) => {
  try {
    const envData = req.body;
    
    if (!envData.name || !envData.value) {
      return res.status(400).json({
        success: false,
        message: '请提供环境变量名称和值'
      });
    }
    
    const result = await qinglongService.addEnv(envData);
    res.json(result);
  } catch (error) {
    logger.error('添加环境变量失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 更新环境变量
router.put('/envs', async (req, res) => {
  try {
    const envData = req.body;
    
    if (!envData.id) {
      return res.status(400).json({
        success: false,
        message: '请提供环境变量ID'
      });
    }
    
    const result = await qinglongService.updateEnv(envData);
    res.json(result);
  } catch (error) {
    logger.error('更新环境变量失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 删除环境变量
router.delete('/envs', async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供要删除的环境变量ID列表'
      });
    }
    
    const result = await qinglongService.deleteEnv(ids);
    res.json(result);
  } catch (error) {
    logger.error('删除环境变量失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 启用环境变量
router.put('/envs/enable', async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供要启用的环境变量ID列表'
      });
    }
    
    const result = await qinglongService.toggleEnv(ids, true);
    res.json(result);
  } catch (error) {
    logger.error('启用环境变量失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 禁用环境变量
router.put('/envs/disable', async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供要禁用的环境变量ID列表'
      });
    }
    
    const result = await qinglongService.toggleEnv(ids, false);
    res.json(result);
  } catch (error) {
    logger.error('禁用环境变量失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

module.exports = router;
