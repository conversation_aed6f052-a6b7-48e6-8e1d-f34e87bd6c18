<template>
  <div class="layout-container">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside :width="isCollapse ? '64px' : '240px'" class="sidebar">
        <div class="logo">
          <img src="/logo.svg" alt="Logo" v-if="!isCollapse" />
          <span v-if="!isCollapse" class="logo-text">青龙管理</span>
          <img src="/logo.svg" alt="Logo" v-else class="logo-mini" />
        </div>
        
        <el-menu
          :default-active="$route.path"
          :collapse="isCollapse"
          :unique-opened="true"
          router
          class="sidebar-menu"
        >
          <el-menu-item
            v-for="route in menuRoutes"
            :key="route.path"
            :index="route.path"
          >
            <el-icon><component :is="route.meta.icon" /></el-icon>
            <template #title>{{ route.meta.title }}</template>
          </el-menu-item>
        </el-menu>
      </el-aside>
      
      <!-- 主内容区 -->
      <el-container>
        <!-- 顶部导航 -->
        <el-header class="header">
          <div class="header-left">
            <el-button
              :icon="isCollapse ? Expand : Fold"
              @click="toggleCollapse"
              text
              class="collapse-btn"
            />
            
            <el-breadcrumb separator="/">
              <el-breadcrumb-item>青龙管理</el-breadcrumb-item>
              <el-breadcrumb-item>{{ currentRoute?.meta?.title }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          
          <div class="header-right">
            <!-- 连接状态 -->
            <div class="connection-status">
              <el-badge
                :type="qinglongStore.isConnected ? 'success' : 'danger'"
                is-dot
              >
                <el-button
                  :type="qinglongStore.isConnected ? 'success' : 'danger'"
                  size="small"
                  plain
                  @click="handleConnectionClick"
                >
                  {{ qinglongStore.isConnected ? '已连接' : '未连接' }}
                </el-button>
              </el-badge>
            </div>
            
            <!-- 主题切换 -->
            <el-button
              :icon="themeStore.isDark ? Sunny : Moon"
              @click="handleThemeToggle"
              text
              class="theme-btn"
              :title="themeStore.isDark ? '切换到浅色主题' : '切换到深色主题'"
            />
            
            <!-- 系统信息 -->
            <el-dropdown trigger="click">
              <el-button :icon="Setting" text class="setting-btn" />
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="showSystemInfo">
                    <el-icon><InfoFilled /></el-icon>
                    系统信息
                  </el-dropdown-item>
                  <el-dropdown-item @click="showAbout">
                    <el-icon><QuestionFilled /></el-icon>
                    关于
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        
        <!-- 主内容 -->
        <el-main class="main-content">
          <router-view v-slot="{ Component }">
            <transition name="fade-transform" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </el-main>
      </el-container>
    </el-container>
    
    <!-- 系统信息对话框 -->
    <el-dialog
      v-model="systemInfoVisible"
      title="系统信息"
      width="600px"
    >
      <div class="system-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="应用版本">
            {{ systemInfo.version }}
          </el-descriptions-item>
          <el-descriptions-item label="运行环境">
            {{ systemInfo.env }}
          </el-descriptions-item>
          <el-descriptions-item label="启动时间">
            {{ systemInfo.startTime }}
          </el-descriptions-item>
          <el-descriptions-item label="运行时长">
            {{ systemInfo.uptime }}
          </el-descriptions-item>
          <el-descriptions-item label="青龙面板">
            <el-tag :type="qinglongStore.isConnected ? 'success' : 'danger'">
              {{ qinglongStore.isConnected ? '已连接' : '未连接' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="AI服务">
            <el-tag type="info">未配置</el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    
    <!-- 关于对话框 -->
    <el-dialog
      v-model="aboutVisible"
      title="关于青龙管理界面"
      width="500px"
    >
      <div class="about-content">
        <div class="about-logo">
          <img src="/logo.svg" alt="Logo" />
          <h3>青龙面板管理界面</h3>
          <p>v1.0.0</p>
        </div>
        
        <el-divider />
        
        <div class="about-description">
          <p>基于Web的青龙面板可视化管理工具，集成AI功能自动生成脚本代码。</p>
          
          <h4>主要功能：</h4>
          <ul>
            <li>🔗 无需登录，直接通过Client ID和Secret连接</li>
            <li>📊 实时日志监控和消息面板</li>
            <li>🤖 AI脚本生成器，支持多种AI模型</li>
            <li>🚀 一键部署和批量操作</li>
            <li>⚙️ 可视化配置管理</li>
          </ul>
          
          <h4>技术栈：</h4>
          <p>Vue.js 3 + Element Plus + Node.js + Express</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useThemeStore } from '@/stores/theme'
import { useQinglongStore } from '@/stores/qinglong'
import {
  Fold,
  Expand,
  Setting,
  Moon,
  Sunny,
  InfoFilled,
  QuestionFilled
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const themeStore = useThemeStore()
const qinglongStore = useQinglongStore()

// 响应式数据
const isCollapse = ref(false)
const systemInfoVisible = ref(false)
const aboutVisible = ref(false)

// 系统信息
const systemInfo = ref({
  version: '1.0.0',
  env: 'development',
  startTime: new Date().toLocaleString(),
  uptime: '0分钟'
})

// 计算属性
const currentRoute = computed(() => route)

const menuRoutes = computed(() => {
  return router.getRoutes()
    .filter(route => route.meta?.title && route.path !== '/')
    .sort((a, b) => (a.meta.order || 0) - (b.meta.order || 0))
})

// 方法
const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value
}

const handleConnectionClick = () => {
  if (!qinglongStore.isConnected) {
    router.push('/settings')
  }
}

const showSystemInfo = () => {
  systemInfoVisible.value = true
  updateSystemInfo()
}

const showAbout = () => {
  aboutVisible.value = true
}

const updateSystemInfo = () => {
  // 更新运行时长等信息
  const startTime = new Date(systemInfo.value.startTime)
  const now = new Date()
  const uptime = Math.floor((now - startTime) / 1000 / 60)
  systemInfo.value.uptime = `${uptime}分钟`
}

const handleThemeToggle = () => {
  // 添加切换动画效果
  const themeBtn = document.querySelector('.theme-btn')
  if (themeBtn) {
    themeBtn.style.transform = 'rotate(360deg)'
    setTimeout(() => {
      themeBtn.style.transform = 'rotate(0deg)'
    }, 300)
  }

  themeStore.toggleTheme()

  // 显示切换提示
  ElMessage.success(`已切换到${themeStore.isDark ? '深色' : '浅色'}主题`)
}

// 生命周期
onMounted(() => {
  // 加载连接信息
  qinglongStore.loadConnectionInfo()
  
  // 如果已配置，尝试连接
  if (qinglongStore.isConfigured) {
    qinglongStore.testConnection()
  }
})
</script>

<style lang="scss" scoped>
.layout-container {
  height: 100vh;
  
  .el-container {
    height: 100%;
  }
}

.sidebar {
  background: var(--el-bg-color-page);
  border-right: 1px solid var(--el-border-color-light);
  transition: all 0.3s var(--transition-function);
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 1px;
    height: 100%;
    background: linear-gradient(180deg,
      transparent 0%,
      var(--el-border-color-light) 20%,
      var(--el-border-color-light) 80%,
      transparent 100%);
  }
  
  .logo {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 20px;
    border-bottom: 1px solid var(--el-border-color-light);
    
    img {
      width: 32px;
      height: 32px;
    }
    
    .logo-text {
      margin-left: 12px;
      font-size: 18px;
      font-weight: bold;
      color: var(--el-text-color-primary);
    }
    
    .logo-mini {
      width: 32px;
      height: 32px;
    }
  }
  
  .sidebar-menu {
    border: none;
    height: calc(100vh - 60px);
    
    .el-menu-item {
      height: 56px;
      line-height: 56px;
      
      &.is-active {
        background-color: var(--el-color-primary-light-9);
        color: var(--el-color-primary);
        
        &::before {
          content: '';
          position: absolute;
          right: 0;
          top: 0;
          bottom: 0;
          width: 3px;
          background-color: var(--el-color-primary);
        }
      }
    }
  }
}

.header {
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  
  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .collapse-btn {
      font-size: 18px;
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .connection-status {
      .el-badge {
        .el-button {
          font-size: 12px;
        }
      }
    }
    
    .theme-btn,
    .setting-btn {
      font-size: 18px;
      transition: all 0.3s var(--transition-function);

      &:hover {
        transform: scale(1.1);
      }
    }

    .theme-btn {
      &:hover {
        color: var(--el-color-warning);
      }
    }
  }
}

.main-content {
  background: var(--el-bg-color-page);
  padding: 20px;
  overflow-y: auto;
}

// 页面切换动画
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

// 系统信息样式
.system-info {
  .el-descriptions {
    margin-top: 20px;
  }
}

// 关于页面样式
.about-content {
  text-align: center;
  
  .about-logo {
    img {
      width: 64px;
      height: 64px;
      margin-bottom: 16px;
    }
    
    h3 {
      margin: 0 0 8px 0;
      color: var(--el-text-color-primary);
    }
    
    p {
      margin: 0;
      color: var(--el-text-color-regular);
    }
  }
  
  .about-description {
    text-align: left;
    
    h4 {
      margin: 16px 0 8px 0;
      color: var(--el-text-color-primary);
    }
    
    ul {
      margin: 8px 0;
      padding-left: 20px;
      
      li {
        margin: 4px 0;
        color: var(--el-text-color-regular);
      }
    }
    
    p {
      margin: 8px 0;
      color: var(--el-text-color-regular);
    }
  }
}
</style>
