# 青龙面板管理界面 - API文档

## 概述

本文档描述了青龙面板管理界面后端API的详细接口规范。

## 基础信息

- **Base URL**: `http://localhost:3000/api`
- **Content-Type**: `application/json`
- **字符编码**: UTF-8

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "data": {},
  "message": "操作成功"
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误信息",
  "error": "详细错误描述"
}
```

## 青龙面板接口

### 1. 配置连接

**POST** `/qinglong/config`

配置青龙面板连接信息。

**请求参数：**
```json
{
  "baseURL": "http://*************:5700",
  "clientId": "your_client_id",
  "clientSecret": "your_client_secret"
}
```

**响应示例：**
```json
{
  "success": true,
  "message": "连接配置成功"
}
```

### 2. 测试连接

**POST** `/qinglong/test`

测试青龙面板连接是否正常。

**请求参数：**
```json
{
  "baseURL": "http://*************:5700",
  "clientId": "your_client_id",
  "clientSecret": "your_client_secret"
}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "version": "2.15.0",
    "system": "linux"
  },
  "message": "连接成功"
}
```

### 3. 获取脚本列表

**GET** `/qinglong/scripts`

获取青龙面板中的所有脚本。

**响应示例：**
```json
{
  "success": true,
  "data": [
    {
      "filename": "example.js",
      "size": 1024,
      "mtime": "2023-12-01T10:00:00.000Z"
    }
  ]
}
```

### 4. 上传脚本

**POST** `/qinglong/scripts`

上传脚本到青龙面板。

**请求参数：**
```json
{
  "filename": "example.js",
  "content": "console.log('Hello World');"
}
```

### 5. 获取定时任务

**GET** `/qinglong/crons`

获取所有定时任务。

**响应示例：**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "示例任务",
      "command": "node example.js",
      "schedule": "0 0 * * *",
      "status": 0,
      "isRunning": false
    }
  ]
}
```

### 6. 添加定时任务

**POST** `/qinglong/crons`

添加新的定时任务。

**请求参数：**
```json
{
  "name": "示例任务",
  "command": "node example.js",
  "schedule": "0 0 * * *"
}
```

## AI服务接口

### 1. 配置AI服务

**POST** `/ai/config`

配置AI服务连接信息。

**请求参数：**
```json
{
  "provider": "openai",
  "apiKey": "your_api_key",
  "baseURL": "https://api.openai.com/v1",
  "model": "gpt-4"
}
```

### 2. 生成脚本

**POST** `/ai/generate`

使用AI生成脚本代码。

**请求参数：**
```json
{
  "prompt": "创建一个京东签到脚本",
  "template": "signin",
  "language": "javascript"
}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "code": "// 生成的脚本代码",
    "prompt": "创建一个京东签到脚本",
    "template": "signin",
    "language": "javascript"
  }
}
```

### 3. 获取脚本模板

**GET** `/ai/templates`

获取所有可用的脚本模板。

**响应示例：**
```json
{
  "success": true,
  "data": [
    {
      "id": "signin",
      "name": "每日签到类",
      "description": "用于各种平台的每日签到脚本"
    }
  ]
}
```

## 脚本管理接口

### 1. 获取本地脚本

**GET** `/script/local`

获取本地保存的脚本列表。

### 2. 保存本地脚本

**POST** `/script/local`

保存脚本到本地。

**请求参数：**
```json
{
  "filename": "example.js",
  "content": "console.log('Hello World');",
  "description": "示例脚本"
}
```

### 3. 批量部署

**POST** `/script/batch-deploy`

批量部署脚本到青龙面板。

**请求参数：**
```json
{
  "scripts": [
    {
      "filename": "example.js",
      "createCron": true,
      "cronConfig": {
        "name": "示例任务",
        "schedule": "0 0 * * *"
      }
    }
  ]
}
```

## 日志管理接口

### 1. 获取内存日志

**GET** `/log/memory`

获取内存中的日志记录。

**查询参数：**
- `level`: 日志级别 (error, warn, info, success)
- `source`: 日志来源 (system, qinglong, ai, script)
- `keyword`: 搜索关键词
- `page`: 页码 (默认1)
- `pageSize`: 每页数量 (默认50)

**响应示例：**
```json
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": "1234567890",
        "timestamp": "2023-12-01T10:00:00.000Z",
        "level": "info",
        "message": "系统启动成功",
        "source": "system"
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 50
  }
}
```

### 2. 清空内存日志

**DELETE** `/log/memory`

清空所有内存日志。

### 3. 导出日志

**POST** `/log/export`

导出日志文件。

**请求参数：**
```json
{
  "format": "txt",
  "level": "error",
  "startTime": "2023-12-01T00:00:00.000Z",
  "endTime": "2023-12-01T23:59:59.000Z"
}
```

## WebSocket事件

### 连接地址
`ws://localhost:3000/socket.io/`

### 事件列表

#### 1. 加入日志房间
```javascript
socket.emit('join-log-room')
```

#### 2. 接收新日志
```javascript
socket.on('new-log', (log) => {
  console.log('新日志:', log)
})
```

#### 3. 接收最近日志
```javascript
socket.on('recent-logs', (logs) => {
  console.log('最近日志:', logs)
})
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 使用示例

### JavaScript (Axios)

```javascript
import axios from 'axios'

const api = axios.create({
  baseURL: 'http://localhost:3000/api',
  timeout: 30000
})

// 测试青龙面板连接
async function testConnection() {
  try {
    const response = await api.post('/qinglong/test', {
      baseURL: 'http://*************:5700',
      clientId: 'your_client_id',
      clientSecret: 'your_client_secret'
    })
    
    console.log('连接成功:', response.data)
  } catch (error) {
    console.error('连接失败:', error.response.data)
  }
}

// 生成AI脚本
async function generateScript() {
  try {
    const response = await api.post('/ai/generate', {
      prompt: '创建一个京东签到脚本',
      template: 'signin'
    })
    
    console.log('生成成功:', response.data.code)
  } catch (error) {
    console.error('生成失败:', error.response.data)
  }
}
```

### WebSocket连接

```javascript
import { io } from 'socket.io-client'

const socket = io('http://localhost:3000')

socket.on('connect', () => {
  console.log('WebSocket连接成功')
  
  // 加入日志房间
  socket.emit('join-log-room')
})

socket.on('new-log', (log) => {
  console.log('收到新日志:', log)
})

socket.on('disconnect', () => {
  console.log('WebSocket连接断开')
})
```

## 注意事项

1. **认证**: 所有青龙面板相关接口都需要先配置连接信息
2. **限流**: AI接口有调用频率限制，请合理使用
3. **文件大小**: 脚本文件大小限制为10MB
4. **并发**: 建议同时运行的脚本数量不超过10个
5. **日志**: 内存日志最多保存1000条，超出会自动清理

## 更新日志

### v1.0.0 (2023-12-01)
- 初始版本发布
- 支持青龙面板基础操作
- 集成AI脚本生成功能
- 实现WebSocket实时通信
