<template>
  <div class="logs">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="title">消息面板</span>
          <div class="header-actions">
            <el-button
              type="primary"
              size="small"
              @click="refreshLogs"
              :loading="loading"
            >
              刷新
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="clearLogs"
            >
              清空
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- 筛选器 -->
      <div class="filters">
        <el-row :gutter="16">
          <el-col :xs="24" :sm="8" :md="6">
            <el-select
              v-model="filters.level"
              placeholder="日志级别"
              clearable
              @change="handleFilterChange"
            >
              <el-option label="全部" value="" />
              <el-option label="错误" value="error" />
              <el-option label="警告" value="warn" />
              <el-option label="信息" value="info" />
              <el-option label="成功" value="success" />
            </el-select>
          </el-col>
          
          <el-col :xs="24" :sm="8" :md="6">
            <el-select
              v-model="filters.source"
              placeholder="日志来源"
              clearable
              @change="handleFilterChange"
            >
              <el-option label="全部" value="" />
              <el-option label="系统" value="system" />
              <el-option label="青龙面板" value="qinglong" />
              <el-option label="AI服务" value="ai" />
              <el-option label="脚本" value="script" />
            </el-select>
          </el-col>
          
          <el-col :xs="24" :sm="8" :md="12">
            <el-input
              v-model="filters.keyword"
              placeholder="搜索关键词"
              clearable
              @input="handleFilterChange"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
        </el-row>
      </div>
      
      <!-- 日志列表 -->
      <div class="logs-container" v-loading="loading">
        <div
          v-for="log in logs"
          :key="log.id"
          class="log-item"
        >
          <div class="log-time">
            {{ formatTime(log.timestamp) }}
          </div>
          <div class="log-content">
            <span class="log-level" :class="log.level">
              {{ log.level.toUpperCase() }}
            </span>
            <span class="log-source">[{{ log.source }}]</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
        
        <div v-if="logs.length === 0 && !loading" class="empty-logs">
          <el-empty description="暂无日志记录" />
        </div>
      </div>
      
      <!-- 分页 -->
      <div class="pagination" v-if="total > 0">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { logApi } from '@/api/log'
import { io } from 'socket.io-client'
import { Search } from '@element-plus/icons-vue'

// 响应式数据
const logs = ref([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(50)

const filters = reactive({
  level: '',
  source: '',
  keyword: ''
})

let socket = null
let filterTimer = null

// 方法
const loadLogs = async () => {
  try {
    loading.value = true
    
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      ...filters
    }
    
    const response = await logApi.getMemoryLogs(params)
    
    if (response.success) {
      logs.value = response.data.logs
      total.value = response.data.total
    }
  } catch (error) {
    console.error('加载日志失败:', error)
  } finally {
    loading.value = false
  }
}

const refreshLogs = () => {
  loadLogs()
}

const clearLogs = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有日志吗？此操作不可恢复。',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await logApi.clearMemoryLogs()
    ElMessage.success('日志已清空')
    await loadLogs()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空日志失败:', error)
    }
  }
}

const handleFilterChange = () => {
  // 防抖处理
  if (filterTimer) {
    clearTimeout(filterTimer)
  }
  
  filterTimer = setTimeout(() => {
    currentPage.value = 1
    loadLogs()
  }, 300)
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadLogs()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadLogs()
}

const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  return date.toLocaleString()
}

const initSocket = () => {
  socket = io({
    transports: ['websocket']
  })
  
  socket.on('connect', () => {
    socket.emit('join-log-room')
  })
  
  socket.on('new-log', (log) => {
    // 如果当前在第一页且没有筛选条件，实时添加新日志
    if (currentPage.value === 1 && !filters.level && !filters.source && !filters.keyword) {
      logs.value.unshift(log)
      
      // 保持页面大小限制
      if (logs.value.length > pageSize.value) {
        logs.value.pop()
      }
      
      total.value++
    }
  })
}

// 生命周期
onMounted(() => {
  loadLogs()
  initSocket()
})

onUnmounted(() => {
  if (socket) {
    socket.disconnect()
  }
  
  if (filterTimer) {
    clearTimeout(filterTimer)
  }
})
</script>

<style lang="scss" scoped>
.logs {
  .filters {
    margin-bottom: 20px;
    
    .el-col {
      margin-bottom: 8px;
    }
  }
  
  .logs-container {
    min-height: 400px;
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 6px;
    background-color: var(--el-fill-color-blank);
    
    .log-item {
      display: flex;
      align-items: flex-start;
      padding: 12px 16px;
      border-bottom: 1px solid var(--el-border-color-lighter);
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 13px;
      
      &:last-child {
        border-bottom: none;
      }
      
      &:hover {
        background-color: var(--el-fill-color-lighter);
      }
      
      .log-time {
        width: 140px;
        color: var(--el-text-color-secondary);
        flex-shrink: 0;
        font-size: 12px;
      }
      
      .log-content {
        flex: 1;
        margin-left: 12px;
        word-break: break-all;
        
        .log-level {
          margin-right: 8px;
          font-weight: 600;
          
          &.error {
            color: var(--el-color-danger);
          }
          
          &.warn {
            color: var(--el-color-warning);
          }
          
          &.info {
            color: var(--el-color-primary);
          }
          
          &.success {
            color: var(--el-color-success);
          }
        }
        
        .log-source {
          margin-right: 8px;
          color: var(--el-text-color-secondary);
          font-size: 12px;
        }
        
        .log-message {
          color: var(--el-text-color-primary);
        }
      }
    }
    
    .empty-logs {
      padding: 60px 0;
    }
  }
  
  .pagination {
    margin-top: 20px;
    text-align: center;
  }
}
</style>
