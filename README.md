# 青龙面板管理界面

<div align="center">

![Logo](frontend/public/logo.svg)

**基于Web的青龙面板可视化管理工具，集成AI功能自动生成脚本代码**

[![Node.js](https://img.shields.io/badge/Node.js-16+-green.svg)](https://nodejs.org/)
[![Vue.js](https://img.shields.io/badge/Vue.js-3.0+-blue.svg)](https://vuejs.org/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

[功能特性](#功能特性) • [快速开始](#快速开始) • [使用说明](#使用说明) • [API文档](#api文档)

</div>

## 功能特性

- 🔗 **无需登录**：直接通过青龙面板的Client ID和Client Secret进行连接认证
- 📊 **消息面板**：实时展示系统日志和任务执行状态，支持分级显示和搜索
- 🤖 **AI脚本生成器**：支持多AI模型，中文自然语言输入生成脚本
- 🚀 **一键部署**：脚本上传、批量操作、Cron配置、版本管理
- ⚙️ **连接设置**：青龙面板和AI服务配置管理

## 技术架构

- **前端**：Vue.js 3 + Element Plus + Monaco Editor
- **后端**：Node.js + Express + Socket.io
- **通信**：RESTful API + WebSocket实时通信

## 项目结构

```
qinglong-manager/
├── frontend/          # Vue.js前端项目
│   ├── src/
│   │   ├── components/    # 组件
│   │   ├── views/         # 页面视图
│   │   ├── api/           # API接口
│   │   ├── utils/         # 工具函数
│   │   └── assets/        # 静态资源
│   ├── public/
│   └── package.json
├── backend/           # Node.js后端项目
│   ├── src/
│   │   ├── routes/        # 路由
│   │   ├── services/      # 服务层
│   │   ├── utils/         # 工具函数
│   │   └── config/        # 配置文件
│   ├── logs/              # 日志文件
│   └── package.json
├── docs/              # 项目文档
└── README.md
```

## 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖

```bash
# 安装后端依赖
cd backend
npm install

# 安装前端依赖
cd ../frontend
npm install
```

### 启动项目

```bash
# 启动后端服务
cd backend
npm run dev

# 启动前端服务
cd ../frontend
npm run dev
```

### 配置说明

1. 在青龙面板中获取Client ID和Client Secret：
   - 登录青龙面板
   - 进入"系统设置" -> "应用设置"
   - 创建新应用或使用现有应用的凭据

2. 在管理界面的"连接设置"中配置：
   - 青龙面板地址（如：http://192.168.1.100:5700）
   - Client ID和Client Secret
   - AI服务配置（可选）

## 开发计划

- [x] 项目初始化和架构搭建
- [ ] 后端API服务开发
- [ ] 前端Vue.js应用开发
- [ ] 消息面板模块实现
- [ ] AI脚本生成器模块实现
- [ ] 一键部署模块实现
- [ ] 连接设置模块实现
- [ ] WebSocket实时通信实现
- [ ] 界面美化和主题切换
- [ ] 测试和文档编写

## 许可证

MIT License
