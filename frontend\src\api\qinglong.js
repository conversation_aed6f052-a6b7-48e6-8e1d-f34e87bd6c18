import request from './request'

export const qinglongApi = {
  // 配置连接
  configConnection(data) {
    return request.post('/qinglong/config', data)
  },
  
  // 测试连接
  testConnection(data) {
    return request.post('/qinglong/test', data)
  },
  
  // 脚本管理
  getScripts() {
    return request.get('/qinglong/scripts')
  },
  
  getScriptContent(filename) {
    return request.get(`/qinglong/scripts/${filename}`)
  },
  
  uploadScript(data) {
    return request.post('/qinglong/scripts', data)
  },
  
  deleteScript(filename) {
    return request.delete(`/qinglong/scripts/${filename}`)
  },
  
  // 定时任务管理
  getCrons() {
    return request.get('/qinglong/crons')
  },
  
  addCron(data) {
    return request.post('/qinglong/crons', data)
  },
  
  updateCron(data) {
    return request.put('/qinglong/crons', data)
  },
  
  deleteCrons(ids) {
    return request.delete('/qinglong/crons', { data: { ids } })
  },
  
  enableCrons(ids) {
    return request.put('/qinglong/crons/enable', { ids })
  },
  
  disableCrons(ids) {
    return request.put('/qinglong/crons/disable', { ids })
  },
  
  runCrons(ids) {
    return request.put('/qinglong/crons/run', { ids })
  },
  
  // 日志管理
  getLogs(filename) {
    return request.get(`/qinglong/logs/${filename}`)
  },
  
  // 环境变量管理
  getEnvs() {
    return request.get('/qinglong/envs')
  },
  
  addEnv(data) {
    return request.post('/qinglong/envs', data)
  },
  
  updateEnv(data) {
    return request.put('/qinglong/envs', data)
  },
  
  deleteEnvs(ids) {
    return request.delete('/qinglong/envs', { data: { ids } })
  },
  
  enableEnvs(ids) {
    return request.put('/qinglong/envs/enable', { ids })
  },
  
  disableEnvs(ids) {
    return request.put('/qinglong/envs/disable', { ids })
  }
}
