const axios = require('axios');
const logger = require('../utils/logger');
const config = require('../config/config');
const fs = require('fs-extra');
const path = require('path');

class AIService {
  constructor() {
    this.provider = null;
    this.apiKey = null;
    this.baseURL = null;
    this.model = null;
    this.axiosInstance = null;
    this.generationHistory = [];
    this.templates = this.loadTemplates();
  }
  
  // 配置AI服务
  configure(provider, apiKey, baseURL = null, model = null) {
    this.provider = provider;
    this.apiKey = apiKey;
    this.baseURL = baseURL || config.ai.providers[provider]?.baseURL;
    this.model = model || config.ai.providers[provider]?.model;
    
    // 创建axios实例
    this.axiosInstance = axios.create({
      baseURL: this.baseURL,
      timeout: config.ai.timeout,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    // 设置认证头
    if (provider === 'openai') {
      this.axiosInstance.defaults.headers.Authorization = `Bearer ${apiKey}`;
    } else if (provider === 'claude') {
      this.axiosInstance.defaults.headers['x-api-key'] = apiKey;
      this.axiosInstance.defaults.headers['anthropic-version'] = '2023-06-01';
    }
    
    logger.ai(`AI服务已配置: ${provider} - ${this.model}`);
  }
  
  // 测试连接
  async testConnection() {
    if (!this.provider || !this.apiKey) {
      throw new Error('AI服务未配置');
    }
    
    try {
      logger.ai('正在测试AI服务连接...');
      
      let response;
      if (this.provider === 'openai') {
        response = await this.axiosInstance.get('/models');
      } else if (this.provider === 'claude') {
        // Claude没有直接的测试接口，尝试发送一个简单请求
        response = await this.axiosInstance.post('/messages', {
          model: this.model,
          max_tokens: 10,
          messages: [{ role: 'user', content: 'Hello' }]
        });
      } else if (this.provider === 'ollama') {
        response = await this.axiosInstance.get('/tags');
      }
      
      logger.ai('AI服务连接测试成功');
      return {
        success: true,
        message: '连接成功',
        data: {
          provider: this.provider,
          model: this.model
        }
      };
    } catch (error) {
      logger.ai(`AI服务连接测试失败: ${error.message}`, 'error');
      return {
        success: false,
        message: `连接失败: ${error.message}`
      };
    }
  }
  
  // 生成脚本
  async generateScript(prompt, template = null, language = 'javascript') {
    if (!this.provider || !this.apiKey) {
      throw new Error('AI服务未配置');
    }
    
    try {
      logger.ai(`开始生成脚本: ${prompt.substring(0, 50)}...`);
      
      // 构建系统提示
      let systemPrompt = this.buildSystemPrompt(language, template);
      
      // 构建用户提示
      let userPrompt = this.buildUserPrompt(prompt, template);
      
      let response;
      let generatedCode = '';
      
      if (this.provider === 'openai') {
        response = await this.axiosInstance.post('/chat/completions', {
          model: this.model,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ],
          max_tokens: config.ai.providers.openai.maxTokens,
          temperature: config.ai.providers.openai.temperature
        });
        
        generatedCode = response.data.choices[0].message.content;
      } else if (this.provider === 'claude') {
        response = await this.axiosInstance.post('/messages', {
          model: this.model,
          max_tokens: config.ai.providers.claude.maxTokens,
          temperature: config.ai.providers.claude.temperature,
          system: systemPrompt,
          messages: [{ role: 'user', content: userPrompt }]
        });
        
        generatedCode = response.data.content[0].text;
      } else if (this.provider === 'ollama') {
        response = await this.axiosInstance.post('/generate', {
          model: this.model,
          prompt: `${systemPrompt}\n\n${userPrompt}`,
          stream: false
        });
        
        generatedCode = response.data.response;
      }
      
      // 清理生成的代码
      generatedCode = this.cleanGeneratedCode(generatedCode);
      
      logger.ai('脚本生成成功');
      
      return {
        success: true,
        data: {
          code: generatedCode,
          prompt,
          template,
          language,
          provider: this.provider,
          model: this.model,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      logger.ai(`脚本生成失败: ${error.message}`, 'error');
      throw new Error(`脚本生成失败: ${error.message}`);
    }
  }
  
  // 优化脚本
  async optimizeScript(code, requirements = '') {
    if (!this.provider || !this.apiKey) {
      throw new Error('AI服务未配置');
    }
    
    try {
      logger.ai('开始优化脚本...');
      
      const systemPrompt = `你是一个专业的JavaScript代码优化专家。请优化提供的代码，使其更高效、更易读、更符合最佳实践。
      
优化要求：
1. 提高代码性能和效率
2. 增强代码可读性和可维护性
3. 遵循JavaScript最佳实践
4. 添加必要的错误处理
5. 优化异步操作
6. 减少代码重复

请只返回优化后的代码，不要包含解释。`;
      
      const userPrompt = `请优化以下代码：

${requirements ? `特殊要求：${requirements}\n\n` : ''}代码：
\`\`\`javascript
${code}
\`\`\``;
      
      let response;
      let optimizedCode = '';
      
      if (this.provider === 'openai') {
        response = await this.axiosInstance.post('/chat/completions', {
          model: this.model,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ],
          max_tokens: config.ai.providers.openai.maxTokens,
          temperature: 0.3
        });
        
        optimizedCode = response.data.choices[0].message.content;
      } else if (this.provider === 'claude') {
        response = await this.axiosInstance.post('/messages', {
          model: this.model,
          max_tokens: config.ai.providers.claude.maxTokens,
          temperature: 0.3,
          system: systemPrompt,
          messages: [{ role: 'user', content: userPrompt }]
        });
        
        optimizedCode = response.data.content[0].text;
      }
      
      optimizedCode = this.cleanGeneratedCode(optimizedCode);
      
      logger.ai('脚本优化成功');
      
      return {
        success: true,
        data: {
          originalCode: code,
          optimizedCode,
          requirements,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      logger.ai(`脚本优化失败: ${error.message}`, 'error');
      throw new Error(`脚本优化失败: ${error.message}`);
    }
  }
  
  // 解释代码
  async explainCode(code) {
    if (!this.provider || !this.apiKey) {
      throw new Error('AI服务未配置');
    }
    
    try {
      logger.ai('开始解释代码...');
      
      const systemPrompt = `你是一个专业的代码解释专家。请用中文详细解释提供的代码，包括：
1. 代码的主要功能和目的
2. 关键函数和变量的作用
3. 代码的执行流程
4. 使用的技术和方法
5. 可能的改进建议

请用清晰、易懂的语言进行解释。`;
      
      const userPrompt = `请解释以下代码：

\`\`\`javascript
${code}
\`\`\``;
      
      let response;
      let explanation = '';
      
      if (this.provider === 'openai') {
        response = await this.axiosInstance.post('/chat/completions', {
          model: this.model,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ],
          max_tokens: config.ai.providers.openai.maxTokens,
          temperature: 0.5
        });
        
        explanation = response.data.choices[0].message.content;
      } else if (this.provider === 'claude') {
        response = await this.axiosInstance.post('/messages', {
          model: this.model,
          max_tokens: config.ai.providers.claude.maxTokens,
          temperature: 0.5,
          system: systemPrompt,
          messages: [{ role: 'user', content: userPrompt }]
        });
        
        explanation = response.data.content[0].text;
      }
      
      logger.ai('代码解释成功');
      
      return {
        success: true,
        data: {
          code,
          explanation,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      logger.ai(`代码解释失败: ${error.message}`, 'error');
      throw new Error(`代码解释失败: ${error.message}`);
    }
  }
  
  // 构建系统提示
  buildSystemPrompt(language, template) {
    let prompt = `你是一个专业的${language === 'javascript' ? 'JavaScript' : language}脚本开发专家，专门为青龙面板开发自动化脚本。

请根据用户需求生成高质量的脚本代码，要求：
1. 代码结构清晰，注释详细
2. 包含完整的错误处理机制
3. 支持青龙面板的环境变量读取
4. 遵循最佳编程实践
5. 代码具有良好的可维护性

请只返回代码，不要包含其他解释文字。`;
    
    if (template) {
      prompt += `\n\n请基于以下模板进行开发：\n${template.content}`;
    }
    
    return prompt;
  }
  
  // 构建用户提示
  buildUserPrompt(prompt, template) {
    let userPrompt = `请根据以下需求生成脚本：\n\n${prompt}`;
    
    if (template) {
      userPrompt += `\n\n模板类型：${template.name}\n模板描述：${template.description}`;
    }
    
    return userPrompt;
  }
  
  // 清理生成的代码
  cleanGeneratedCode(code) {
    // 移除markdown代码块标记
    code = code.replace(/```(?:javascript|js|python|py|bash|sh)?\n?/g, '');
    code = code.replace(/```\n?/g, '');
    
    // 移除多余的空行
    code = code.replace(/\n{3,}/g, '\n\n');
    
    // 去除首尾空白
    code = code.trim();
    
    return code;
  }
  
  // 加载脚本模板
  loadTemplates() {
    return [
      {
        id: 'signin',
        name: '每日签到类',
        description: '用于各种平台的每日签到脚本',
        content: `// 每日签到脚本模板
const axios = require('axios');

// 从环境变量读取配置
const COOKIE = process.env.COOKIE || '';
const PUSH_KEY = process.env.PUSH_KEY || '';

class SignInScript {
  constructor() {
    this.name = '每日签到';
    this.cookie = COOKIE;
  }
  
  async main() {
    if (!this.cookie) {
      console.log('❌ 请设置COOKIE环境变量');
      return;
    }
    
    try {
      await this.signIn();
    } catch (error) {
      console.log('❌ 签到失败:', error.message);
    }
  }
  
  async signIn() {
    // 在此实现签到逻辑
    console.log('✅ 签到成功');
  }
}

new SignInScript().main();`
      },
      {
        id: 'monitor',
        name: '商品监控类',
        description: '用于监控商品价格、库存等信息',
        content: `// 商品监控脚本模板
const axios = require('axios');

class ProductMonitor {
  constructor() {
    this.name = '商品监控';
    this.checkInterval = 60000; // 检查间隔(毫秒)
  }
  
  async main() {
    console.log('🔍 开始监控商品...');
    
    while (true) {
      try {
        await this.checkProduct();
        await this.sleep(this.checkInterval);
      } catch (error) {
        console.log('❌ 监控出错:', error.message);
        await this.sleep(5000);
      }
    }
  }
  
  async checkProduct() {
    // 在此实现商品检查逻辑
    console.log('✅ 商品检查完成');
  }
  
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

new ProductMonitor().main();`
      },
      {
        id: 'purchase',
        name: '自动抢购类',
        description: '用于自动抢购商品的脚本',
        content: `// 自动抢购脚本模板
const axios = require('axios');

class AutoPurchase {
  constructor() {
    this.name = '自动抢购';
    this.maxRetries = 10;
  }
  
  async main() {
    console.log('🛒 开始自动抢购...');
    
    for (let i = 0; i < this.maxRetries; i++) {
      try {
        const success = await this.attemptPurchase();
        if (success) {
          console.log('✅ 抢购成功!');
          break;
        }
      } catch (error) {
        console.log(\`❌ 第\${i + 1}次尝试失败:\`, error.message);
      }
      
      await this.sleep(1000);
    }
  }
  
  async attemptPurchase() {
    // 在此实现抢购逻辑
    return false;
  }
  
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

new AutoPurchase().main();`
      },
      {
        id: 'crawler',
        name: '数据采集类',
        description: '用于采集网站数据的脚本',
        content: `// 数据采集脚本模板
const axios = require('axios');
const cheerio = require('cheerio');

class DataCrawler {
  constructor() {
    this.name = '数据采集';
    this.headers = {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    };
  }
  
  async main() {
    console.log('📊 开始数据采集...');
    
    try {
      const data = await this.crawlData();
      await this.saveData(data);
      console.log('✅ 数据采集完成');
    } catch (error) {
      console.log('❌ 数据采集失败:', error.message);
    }
  }
  
  async crawlData() {
    // 在此实现数据采集逻辑
    return [];
  }
  
  async saveData(data) {
    // 在此实现数据保存逻辑
    console.log(\`📝 保存了 \${data.length} 条数据\`);
  }
}

new DataCrawler().main();`
      }
    ];
  }
  
  // 获取模板
  getTemplates() {
    return this.templates;
  }
  
  // 代码语法检查
  async validateCode(code, language = 'javascript') {
    try {
      // 简单的语法检查
      const issues = [];
      
      if (language === 'javascript') {
        // 检查常见语法错误
        if (!code.includes('function') && !code.includes('=>') && !code.includes('class')) {
          issues.push({
            type: 'warning',
            message: '代码中没有发现函数或类定义'
          });
        }
        
        // 检查括号匹配
        const openBraces = (code.match(/{/g) || []).length;
        const closeBraces = (code.match(/}/g) || []).length;
        if (openBraces !== closeBraces) {
          issues.push({
            type: 'error',
            message: '大括号不匹配'
          });
        }
        
        const openParens = (code.match(/\(/g) || []).length;
        const closeParens = (code.match(/\)/g) || []).length;
        if (openParens !== closeParens) {
          issues.push({
            type: 'error',
            message: '小括号不匹配'
          });
        }
      }
      
      return {
        success: true,
        data: {
          isValid: issues.filter(i => i.type === 'error').length === 0,
          issues
        }
      };
    } catch (error) {
      logger.ai(`代码验证失败: ${error.message}`, 'error');
      return {
        success: false,
        message: error.message
      };
    }
  }
  
  // 保存生成记录
  async saveGenerationRecord(record) {
    try {
      const newRecord = {
        id: Date.now().toString(),
        ...record,
        timestamp: new Date().toISOString()
      };
      
      this.generationHistory.unshift(newRecord);
      
      // 保持最多50条记录
      if (this.generationHistory.length > 50) {
        this.generationHistory = this.generationHistory.slice(0, 50);
      }
      
      return {
        success: true,
        data: newRecord
      };
    } catch (error) {
      logger.ai(`保存生成记录失败: ${error.message}`, 'error');
      throw error;
    }
  }
  
  // 获取生成历史
  async getGenerationHistory(page = 1, pageSize = 20) {
    try {
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const records = this.generationHistory.slice(startIndex, endIndex);
      
      return {
        success: true,
        data: {
          records,
          total: this.generationHistory.length,
          page,
          pageSize,
          totalPages: Math.ceil(this.generationHistory.length / pageSize)
        }
      };
    } catch (error) {
      logger.ai(`获取生成历史失败: ${error.message}`, 'error');
      throw error;
    }
  }
  
  // 删除生成记录
  async deleteGenerationRecord(id) {
    try {
      const index = this.generationHistory.findIndex(record => record.id === id);
      if (index === -1) {
        throw new Error('记录不存在');
      }
      
      this.generationHistory.splice(index, 1);
      
      return {
        success: true,
        message: '记录删除成功'
      };
    } catch (error) {
      logger.ai(`删除生成记录失败: ${error.message}`, 'error');
      throw error;
    }
  }
  
  // 切换收藏状态
  async toggleFavorite(id, isFavorite) {
    try {
      const record = this.generationHistory.find(r => r.id === id);
      if (!record) {
        throw new Error('记录不存在');
      }
      
      record.isFavorite = isFavorite;
      
      return {
        success: true,
        message: isFavorite ? '已收藏' : '已取消收藏'
      };
    } catch (error) {
      logger.ai(`更新收藏状态失败: ${error.message}`, 'error');
      throw error;
    }
  }
  
  // 语音转文字 (占位符实现)
  async speechToText(audioData) {
    // 这里需要集成语音识别服务，如百度、腾讯等
    // 目前返回占位符响应
    return {
      success: false,
      message: '语音转文字功能暂未实现，请手动输入文字'
    };
  }
}

// 创建单例实例
const aiService = new AIService();

module.exports = aiService;
