const express = require('express');
const router = express.Router();
const logger = require('../utils/logger');

// 获取内存中的日志
router.get('/memory', async (req, res) => {
  try {
    const {
      level,
      keyword,
      startTime,
      endTime,
      page = 1,
      pageSize = 50,
      source
    } = req.query;
    
    const filter = {
      level,
      keyword,
      startTime,
      endTime,
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      source
    };
    
    const result = logger.getMemoryLogs(filter);
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error('获取内存日志失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 清空内存日志
router.delete('/memory', async (req, res) => {
  try {
    logger.clearMemoryLogs();
    
    res.json({
      success: true,
      message: '内存日志已清空'
    });
  } catch (error) {
    logger.error('清空内存日志失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 导出日志
router.post('/export', async (req, res) => {
  try {
    const {
      level,
      keyword,
      startTime,
      endTime,
      format = 'txt'
    } = req.body;
    
    const filter = {
      level,
      keyword,
      startTime,
      endTime,
      page: 1,
      pageSize: 10000 // 导出时获取更多日志
    };
    
    const result = logger.getMemoryLogs(filter);
    
    let content = '';
    let contentType = 'text/plain';
    let filename = `logs-${Date.now()}`;
    
    if (format === 'txt') {
      content = result.logs.map(log => 
        `${log.timestamp} [${log.level.toUpperCase()}] [${log.source}]: ${log.message}`
      ).join('\n');
      filename += '.txt';
    } else if (format === 'json') {
      content = JSON.stringify(result.logs, null, 2);
      contentType = 'application/json';
      filename += '.json';
    } else if (format === 'csv') {
      const headers = 'Timestamp,Level,Source,Message\n';
      const rows = result.logs.map(log => 
        `"${log.timestamp}","${log.level}","${log.source}","${log.message.replace(/"/g, '""')}"`
      ).join('\n');
      content = headers + rows;
      contentType = 'text/csv';
      filename += '.csv';
    }
    
    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.send(content);
  } catch (error) {
    logger.error('导出日志失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 获取日志统计信息
router.get('/stats', async (req, res) => {
  try {
    const allLogs = logger.getMemoryLogs({ page: 1, pageSize: 10000 });
    
    const stats = {
      total: allLogs.total,
      levels: {},
      sources: {},
      recentActivity: []
    };
    
    // 统计各级别日志数量
    allLogs.logs.forEach(log => {
      stats.levels[log.level] = (stats.levels[log.level] || 0) + 1;
      stats.sources[log.source] = (stats.sources[log.source] || 0) + 1;
    });
    
    // 获取最近1小时的活动
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString();
    const recentLogs = allLogs.logs.filter(log => log.timestamp > oneHourAgo);
    
    // 按5分钟间隔统计
    const intervals = {};
    recentLogs.forEach(log => {
      const time = new Date(log.timestamp);
      const interval = new Date(Math.floor(time.getTime() / (5 * 60 * 1000)) * (5 * 60 * 1000));
      const key = interval.toISOString();
      
      if (!intervals[key]) {
        intervals[key] = { timestamp: key, count: 0, errors: 0 };
      }
      
      intervals[key].count++;
      if (log.level === 'error') {
        intervals[key].errors++;
      }
    });
    
    stats.recentActivity = Object.values(intervals).sort((a, b) => 
      new Date(a.timestamp) - new Date(b.timestamp)
    );
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    logger.error('获取日志统计失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 添加自定义日志
router.post('/add', async (req, res) => {
  try {
    const { level = 'info', message, source = 'user' } = req.body;
    
    if (!message) {
      return res.status(400).json({
        success: false,
        message: '请提供日志消息'
      });
    }
    
    logger.log(level, message, { source });
    
    res.json({
      success: true,
      message: '日志添加成功'
    });
  } catch (error) {
    logger.error('添加日志失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 获取系统日志文件列表
router.get('/files', async (req, res) => {
  try {
    const fs = require('fs-extra');
    const path = require('path');
    
    const logDir = path.join(__dirname, '../../logs');
    
    if (!await fs.pathExists(logDir)) {
      return res.json({
        success: true,
        data: []
      });
    }
    
    const files = await fs.readdir(logDir);
    const logFiles = [];
    
    for (const file of files) {
      if (file.endsWith('.log')) {
        const filePath = path.join(logDir, file);
        const stats = await fs.stat(filePath);
        
        logFiles.push({
          name: file,
          size: stats.size,
          modified: stats.mtime.toISOString(),
          path: filePath
        });
      }
    }
    
    // 按修改时间排序
    logFiles.sort((a, b) => new Date(b.modified) - new Date(a.modified));
    
    res.json({
      success: true,
      data: logFiles
    });
  } catch (error) {
    logger.error('获取日志文件列表失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 下载日志文件
router.get('/files/:filename', async (req, res) => {
  try {
    const { filename } = req.params;
    const path = require('path');
    const fs = require('fs-extra');
    
    // 安全检查，防止路径遍历攻击
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      return res.status(400).json({
        success: false,
        message: '无效的文件名'
      });
    }
    
    const filePath = path.join(__dirname, '../../logs', filename);
    
    if (!await fs.pathExists(filePath)) {
      return res.status(404).json({
        success: false,
        message: '文件不存在'
      });
    }
    
    res.setHeader('Content-Type', 'text/plain');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);
  } catch (error) {
    logger.error('下载日志文件失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

module.exports = router;
