# 服务器配置
PORT=3000
HOST=0.0.0.0
NODE_ENV=development

# 日志配置
LOG_LEVEL=info

# CORS配置
CORS_ORIGINS=http://localhost:9090,http://127.0.0.1:9090

# 青龙面板配置（可选，也可以通过界面配置）
# QINGLONG_BASE_URL=http://localhost:5700
# QINGLONG_CLIENT_ID=your_client_id
# QINGLONG_CLIENT_SECRET=your_client_secret

# AI服务配置（可选，也可以通过界面配置）
# AI_PROVIDER=openai
# AI_API_KEY=your_api_key
# AI_BASE_URL=https://api.openai.com/v1
# AI_MODEL=gpt-4

# 安全配置
JWT_SECRET=your_jwt_secret_here
BCRYPT_ROUNDS=10

# 文件上传配置
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# 数据库配置（如果需要）
# DB_HOST=localhost
# DB_PORT=3306
# DB_NAME=qinglong_manager
# DB_USER=root
# DB_PASSWORD=password
