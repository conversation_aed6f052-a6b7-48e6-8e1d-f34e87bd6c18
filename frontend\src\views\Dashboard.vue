<template>
  <div class="dashboard">
    <!-- 顶部统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon success">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.scripts }}</div>
              <div class="stat-label">脚本总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon primary">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.crons }}</div>
              <div class="stat-label">定时任务</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon warning">
              <el-icon><Setting /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.envs }}</div>
              <div class="stat-label">环境变量</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon" :class="qinglongStore.isConnected ? 'success' : 'danger'">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ qinglongStore.isConnected ? '已连接' : '未连接' }}</div>
              <div class="stat-label">连接状态</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 主要内容区域 -->
    <el-row :gutter="20" class="content-row">
      <!-- 左侧：快速操作 -->
      <el-col :xs="24" :lg="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span class="title">快速操作</span>
            </div>
          </template>
          
          <div class="quick-actions">
            <el-button
              type="primary"
              :icon="MagicStick"
              size="large"
              class="action-btn"
              @click="$router.push('/ai-generator')"
            >
              AI生成脚本
            </el-button>
            
            <el-button
              type="success"
              :icon="Upload"
              size="large"
              class="action-btn"
              @click="$router.push('/deploy')"
            >
              一键部署
            </el-button>
            
            <el-button
              type="info"
              :icon="ChatLineSquare"
              size="large"
              class="action-btn"
              @click="$router.push('/logs')"
            >
              查看日志
            </el-button>
            
            <el-button
              type="warning"
              :icon="Setting"
              size="large"
              class="action-btn"
              @click="$router.push('/settings')"
            >
              连接设置
            </el-button>
          </div>
        </el-card>
        
        <!-- 系统状态 -->
        <el-card class="mt-4">
          <template #header>
            <div class="card-header">
              <span class="title">系统状态</span>
              <el-button
                type="primary"
                size="small"
                text
                @click="refreshSystemStatus"
                :loading="statusLoading"
              >
                刷新
              </el-button>
            </div>
          </template>
          
          <div class="system-status">
            <div class="status-item">
              <span class="status-label">青龙面板:</span>
              <el-tag
                :type="qinglongStore.isConnected ? 'success' : 'danger'"
                size="small"
              >
                {{ qinglongStore.isConnected ? '已连接' : '未连接' }}
              </el-tag>
            </div>
            
            <div class="status-item">
              <span class="status-label">AI服务:</span>
              <el-tag type="info" size="small">未配置</el-tag>
            </div>
            
            <div class="status-item">
              <span class="status-label">WebSocket:</span>
              <el-tag
                :type="socketConnected ? 'success' : 'danger'"
                size="small"
              >
                {{ socketConnected ? '已连接' : '未连接' }}
              </el-tag>
            </div>
            
            <div class="status-item" v-if="qinglongStore.systemInfo">
              <span class="status-label">面板版本:</span>
              <span class="status-value">{{ qinglongStore.systemInfo.version || 'N/A' }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 右侧：最近活动和日志 -->
      <el-col :xs="24" :lg="16">
        <el-card>
          <template #header>
            <div class="card-header">
              <span class="title">最近活动</span>
              <el-button
                type="primary"
                size="small"
                text
                @click="$router.push('/logs')"
              >
                查看全部
              </el-button>
            </div>
          </template>
          
          <div class="recent-logs" v-loading="logsLoading">
            <div
              v-for="log in recentLogs"
              :key="log.id"
              class="log-item"
            >
              <div class="log-time">
                {{ formatTime(log.timestamp) }}
              </div>
              <div class="log-content">
                <span class="log-level" :class="log.level">
                  {{ log.level.toUpperCase() }}
                </span>
                <span class="log-message">{{ log.message }}</span>
              </div>
            </div>
            
            <div v-if="recentLogs.length === 0" class="empty-logs">
              <el-empty description="暂无日志记录" :image-size="80" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useQinglongStore } from '@/stores/qinglong'
import { logApi } from '@/api/log'
import { io } from 'socket.io-client'
import {
  Document,
  Clock,
  Setting,
  Connection,
  MagicStick,
  Upload,
  ChatLineSquare
} from '@element-plus/icons-vue'

const qinglongStore = useQinglongStore()

// 响应式数据
const stats = reactive({
  scripts: 0,
  crons: 0,
  envs: 0
})

const recentLogs = ref([])
const logsLoading = ref(false)
const statusLoading = ref(false)
const socketConnected = ref(false)

let socket = null

// 方法
const loadStats = async () => {
  try {
    if (qinglongStore.isConnected) {
      // 加载统计数据
      await Promise.all([
        qinglongStore.fetchScripts(),
        qinglongStore.fetchCrons(),
        qinglongStore.fetchEnvs()
      ])
      
      stats.scripts = qinglongStore.scripts.length
      stats.crons = qinglongStore.crons.length
      stats.envs = qinglongStore.envs.length
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const loadRecentLogs = async () => {
  try {
    logsLoading.value = true
    const response = await logApi.getMemoryLogs({
      page: 1,
      pageSize: 10
    })
    
    if (response.success) {
      recentLogs.value = response.data.logs
    }
  } catch (error) {
    console.error('加载最近日志失败:', error)
  } finally {
    logsLoading.value = false
  }
}

const refreshSystemStatus = async () => {
  try {
    statusLoading.value = true
    
    if (qinglongStore.isConfigured) {
      await qinglongStore.testConnection()
    }
    
    await loadStats()
  } catch (error) {
    console.error('刷新系统状态失败:', error)
  } finally {
    statusLoading.value = false
  }
}

const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date
  
  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 1天内
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString()
  }
}

const initSocket = () => {
  socket = io({
    transports: ['websocket']
  })
  
  socket.on('connect', () => {
    socketConnected.value = true
    socket.emit('join-log-room')
  })
  
  socket.on('disconnect', () => {
    socketConnected.value = false
  })
  
  socket.on('new-log', (log) => {
    recentLogs.value.unshift(log)
    if (recentLogs.value.length > 10) {
      recentLogs.value.pop()
    }
  })
}

// 生命周期
onMounted(async () => {
  await loadStats()
  await loadRecentLogs()
  initSocket()
})

onUnmounted(() => {
  if (socket) {
    socket.disconnect()
  }
})
</script>

<style lang="scss" scoped>
.dashboard {
  .stats-row {
    margin-bottom: 20px;
  }
  
  .stat-card {
    .stat-content {
      display: flex;
      align-items: center;
      
      .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        font-size: 24px;
        
        &.primary {
          background-color: var(--el-color-primary-light-9);
          color: var(--el-color-primary);
        }
        
        &.success {
          background-color: var(--el-color-success-light-9);
          color: var(--el-color-success);
        }
        
        &.warning {
          background-color: var(--el-color-warning-light-9);
          color: var(--el-color-warning);
        }
        
        &.danger {
          background-color: var(--el-color-danger-light-9);
          color: var(--el-color-danger);
        }
      }
      
      .stat-info {
        flex: 1;
        
        .stat-value {
          font-size: 28px;
          font-weight: bold;
          color: var(--el-text-color-primary);
          line-height: 1;
          margin-bottom: 4px;
        }
        
        .stat-label {
          font-size: 14px;
          color: var(--el-text-color-secondary);
        }
      }
    }
  }
  
  .content-row {
    .quick-actions {
      display: flex;
      flex-direction: column;
      gap: 12px;
      
      .action-btn {
        width: 100%;
        height: 48px;
        font-size: 16px;
        justify-content: flex-start;
        
        .el-icon {
          margin-right: 8px;
        }
      }
    }
    
    .system-status {
      .status-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid var(--el-border-color-lighter);
        
        &:last-child {
          border-bottom: none;
        }
        
        .status-label {
          font-size: 14px;
          color: var(--el-text-color-regular);
        }
        
        .status-value {
          font-size: 14px;
          color: var(--el-text-color-primary);
        }
      }
    }
    
    .recent-logs {
      max-height: 400px;
      overflow-y: auto;
      
      .log-item {
        display: flex;
        align-items: flex-start;
        padding: 12px 0;
        border-bottom: 1px solid var(--el-border-color-lighter);
        
        &:last-child {
          border-bottom: none;
        }
        
        .log-time {
          width: 80px;
          font-size: 12px;
          color: var(--el-text-color-secondary);
          flex-shrink: 0;
        }
        
        .log-content {
          flex: 1;
          margin-left: 12px;
          
          .log-level {
            margin-right: 8px;
          }
          
          .log-message {
            font-size: 14px;
            color: var(--el-text-color-primary);
            word-break: break-all;
          }
        }
      }
      
      .empty-logs {
        padding: 40px 0;
      }
    }
  }
}

@media (max-width: 768px) {
  .dashboard {
    .stats-row {
      .el-col {
        margin-bottom: 16px;
      }
    }
    
    .content-row {
      .el-col {
        margin-bottom: 16px;
      }
    }
  }
}
</style>
