import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { qinglongApi } from '@/api/qinglong'

export const useQinglongStore = defineStore('qinglong', () => {
  // 连接状态
  const isConnected = ref(false)
  const connectionInfo = ref({
    baseURL: '',
    clientId: '',
    clientSecret: ''
  })
  const systemInfo = ref(null)
  
  // 脚本数据
  const scripts = ref([])
  const scriptsLoading = ref(false)
  
  // 定时任务数据
  const crons = ref([])
  const cronsLoading = ref(false)
  
  // 环境变量数据
  const envs = ref([])
  const envsLoading = ref(false)
  
  // 计算属性
  const isConfigured = computed(() => {
    return connectionInfo.value.baseURL && 
           connectionInfo.value.clientId && 
           connectionInfo.value.clientSecret
  })
  
  // 配置连接信息
  const setConnectionInfo = (info) => {
    connectionInfo.value = { ...info }
    // 保存到本地存储
    localStorage.setItem('qinglong_connection', JSON.stringify(info))
  }
  
  // 从本地存储加载连接信息
  const loadConnectionInfo = () => {
    const saved = localStorage.getItem('qinglong_connection')
    if (saved) {
      try {
        connectionInfo.value = JSON.parse(saved)
      } catch (error) {
        console.error('加载连接信息失败:', error)
      }
    }
  }
  
  // 测试连接
  const testConnection = async () => {
    try {
      const response = await qinglongApi.testConnection(connectionInfo.value)
      if (response.success) {
        isConnected.value = true
        systemInfo.value = response.data
        ElMessage.success('连接成功')
        return true
      } else {
        isConnected.value = false
        ElMessage.error(response.message || '连接失败')
        return false
      }
    } catch (error) {
      isConnected.value = false
      ElMessage.error('连接失败: ' + error.message)
      return false
    }
  }
  
  // 获取脚本列表
  const fetchScripts = async () => {
    if (!isConnected.value) return
    
    scriptsLoading.value = true
    try {
      const response = await qinglongApi.getScripts()
      if (response.success) {
        scripts.value = response.data
      } else {
        ElMessage.error('获取脚本列表失败: ' + response.message)
      }
    } catch (error) {
      ElMessage.error('获取脚本列表失败: ' + error.message)
    } finally {
      scriptsLoading.value = false
    }
  }
  
  // 获取定时任务列表
  const fetchCrons = async () => {
    if (!isConnected.value) return
    
    cronsLoading.value = true
    try {
      const response = await qinglongApi.getCrons()
      if (response.success) {
        crons.value = response.data
      } else {
        ElMessage.error('获取定时任务列表失败: ' + response.message)
      }
    } catch (error) {
      ElMessage.error('获取定时任务列表失败: ' + error.message)
    } finally {
      cronsLoading.value = false
    }
  }
  
  // 获取环境变量列表
  const fetchEnvs = async () => {
    if (!isConnected.value) return
    
    envsLoading.value = true
    try {
      const response = await qinglongApi.getEnvs()
      if (response.success) {
        envs.value = response.data
      } else {
        ElMessage.error('获取环境变量列表失败: ' + response.message)
      }
    } catch (error) {
      ElMessage.error('获取环境变量列表失败: ' + error.message)
    } finally {
      envsLoading.value = false
    }
  }
  
  // 上传脚本
  const uploadScript = async (filename, content) => {
    try {
      const response = await qinglongApi.uploadScript({ filename, content })
      if (response.success) {
        ElMessage.success('脚本上传成功')
        await fetchScripts() // 刷新脚本列表
        return true
      } else {
        ElMessage.error('脚本上传失败: ' + response.message)
        return false
      }
    } catch (error) {
      ElMessage.error('脚本上传失败: ' + error.message)
      return false
    }
  }
  
  // 删除脚本
  const deleteScript = async (filename) => {
    try {
      const response = await qinglongApi.deleteScript(filename)
      if (response.success) {
        ElMessage.success('脚本删除成功')
        await fetchScripts() // 刷新脚本列表
        return true
      } else {
        ElMessage.error('脚本删除失败: ' + response.message)
        return false
      }
    } catch (error) {
      ElMessage.error('脚本删除失败: ' + error.message)
      return false
    }
  }
  
  // 添加定时任务
  const addCron = async (cronData) => {
    try {
      const response = await qinglongApi.addCron(cronData)
      if (response.success) {
        ElMessage.success('定时任务添加成功')
        await fetchCrons() // 刷新任务列表
        return true
      } else {
        ElMessage.error('定时任务添加失败: ' + response.message)
        return false
      }
    } catch (error) {
      ElMessage.error('定时任务添加失败: ' + error.message)
      return false
    }
  }
  
  // 更新定时任务
  const updateCron = async (cronData) => {
    try {
      const response = await qinglongApi.updateCron(cronData)
      if (response.success) {
        ElMessage.success('定时任务更新成功')
        await fetchCrons() // 刷新任务列表
        return true
      } else {
        ElMessage.error('定时任务更新失败: ' + response.message)
        return false
      }
    } catch (error) {
      ElMessage.error('定时任务更新失败: ' + error.message)
      return false
    }
  }
  
  // 删除定时任务
  const deleteCrons = async (ids) => {
    try {
      const response = await qinglongApi.deleteCrons(ids)
      if (response.success) {
        ElMessage.success('定时任务删除成功')
        await fetchCrons() // 刷新任务列表
        return true
      } else {
        ElMessage.error('定时任务删除失败: ' + response.message)
        return false
      }
    } catch (error) {
      ElMessage.error('定时任务删除失败: ' + error.message)
      return false
    }
  }
  
  // 启用/禁用定时任务
  const toggleCrons = async (ids, status) => {
    try {
      const response = await qinglongApi.toggleCrons(ids, status)
      if (response.success) {
        const action = status ? '启用' : '禁用'
        ElMessage.success(`定时任务${action}成功`)
        await fetchCrons() // 刷新任务列表
        return true
      } else {
        ElMessage.error('定时任务状态更新失败: ' + response.message)
        return false
      }
    } catch (error) {
      ElMessage.error('定时任务状态更新失败: ' + error.message)
      return false
    }
  }
  
  // 运行定时任务
  const runCrons = async (ids) => {
    try {
      const response = await qinglongApi.runCrons(ids)
      if (response.success) {
        ElMessage.success('定时任务运行成功')
        return true
      } else {
        ElMessage.error('定时任务运行失败: ' + response.message)
        return false
      }
    } catch (error) {
      ElMessage.error('定时任务运行失败: ' + error.message)
      return false
    }
  }
  
  // 重置状态
  const reset = () => {
    isConnected.value = false
    systemInfo.value = null
    scripts.value = []
    crons.value = []
    envs.value = []
  }
  
  return {
    // 状态
    isConnected,
    connectionInfo,
    systemInfo,
    scripts,
    scriptsLoading,
    crons,
    cronsLoading,
    envs,
    envsLoading,
    
    // 计算属性
    isConfigured,
    
    // 方法
    setConnectionInfo,
    loadConnectionInfo,
    testConnection,
    fetchScripts,
    fetchCrons,
    fetchEnvs,
    uploadScript,
    deleteScript,
    addCron,
    updateCron,
    deleteCrons,
    toggleCrons,
    runCrons,
    reset
  }
})
