const express = require('express');
const router = express.Router();
const scriptService = require('../services/scriptService');
const logger = require('../utils/logger');

// 获取本地脚本列表
router.get('/local', async (req, res) => {
  try {
    const result = await scriptService.getLocalScripts();
    res.json(result);
  } catch (error) {
    logger.error('获取本地脚本列表失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 保存脚本到本地
router.post('/local', async (req, res) => {
  try {
    const { filename, content, description } = req.body;
    
    if (!filename || !content) {
      return res.status(400).json({
        success: false,
        message: '请提供文件名和内容'
      });
    }
    
    const result = await scriptService.saveLocalScript(filename, content, description);
    res.json(result);
  } catch (error) {
    logger.error('保存本地脚本失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 获取本地脚本内容
router.get('/local/:filename', async (req, res) => {
  try {
    const { filename } = req.params;
    const result = await scriptService.getLocalScriptContent(filename);
    res.json(result);
  } catch (error) {
    logger.error('获取本地脚本内容失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 删除本地脚本
router.delete('/local/:filename', async (req, res) => {
  try {
    const { filename } = req.params;
    const result = await scriptService.deleteLocalScript(filename);
    res.json(result);
  } catch (error) {
    logger.error('删除本地脚本失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 获取脚本版本历史
router.get('/versions/:filename', async (req, res) => {
  try {
    const { filename } = req.params;
    const result = await scriptService.getScriptVersions(filename);
    res.json(result);
  } catch (error) {
    logger.error('获取脚本版本历史失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 回滚脚本版本
router.post('/rollback', async (req, res) => {
  try {
    const { filename, version } = req.body;
    
    if (!filename || !version) {
      return res.status(400).json({
        success: false,
        message: '请提供文件名和版本号'
      });
    }
    
    const result = await scriptService.rollbackScript(filename, version);
    res.json(result);
  } catch (error) {
    logger.error('回滚脚本版本失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 批量部署脚本
router.post('/batch-deploy', async (req, res) => {
  try {
    const { scripts } = req.body;
    
    if (!scripts || !Array.isArray(scripts) || scripts.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供要部署的脚本列表'
      });
    }
    
    const result = await scriptService.batchDeploy(scripts);
    res.json(result);
  } catch (error) {
    logger.error('批量部署脚本失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 检查脚本依赖
router.post('/check-dependencies', async (req, res) => {
  try {
    const { content } = req.body;
    
    if (!content) {
      return res.status(400).json({
        success: false,
        message: '请提供脚本内容'
      });
    }
    
    const result = await scriptService.checkDependencies(content);
    res.json(result);
  } catch (error) {
    logger.error('检查脚本依赖失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 生成Cron表达式
router.post('/generate-cron', async (req, res) => {
  try {
    const { type, config } = req.body;
    
    if (!type || !config) {
      return res.status(400).json({
        success: false,
        message: '请提供Cron类型和配置'
      });
    }
    
    const result = scriptService.generateCronExpression(type, config);
    res.json(result);
  } catch (error) {
    logger.error('生成Cron表达式失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 解析Cron表达式
router.post('/parse-cron', async (req, res) => {
  try {
    const { expression } = req.body;
    
    if (!expression) {
      return res.status(400).json({
        success: false,
        message: '请提供Cron表达式'
      });
    }
    
    const result = scriptService.parseCronExpression(expression);
    res.json(result);
  } catch (error) {
    logger.error('解析Cron表达式失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 导出脚本
router.post('/export', async (req, res) => {
  try {
    const { filenames } = req.body;
    
    if (!filenames || !Array.isArray(filenames) || filenames.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供要导出的脚本文件名列表'
      });
    }
    
    const result = await scriptService.exportScripts(filenames);
    
    if (result.success) {
      res.setHeader('Content-Type', 'application/zip');
      res.setHeader('Content-Disposition', `attachment; filename="scripts-${Date.now()}.zip"`);
      res.send(result.data);
    } else {
      res.status(500).json(result);
    }
  } catch (error) {
    logger.error('导出脚本失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 导入脚本
router.post('/import', async (req, res) => {
  try {
    const { files } = req.body;
    
    if (!files || !Array.isArray(files) || files.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供要导入的文件列表'
      });
    }
    
    const result = await scriptService.importScripts(files);
    res.json(result);
  } catch (error) {
    logger.error('导入脚本失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

module.exports = router;
