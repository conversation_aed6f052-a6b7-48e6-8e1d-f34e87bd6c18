const express = require('express');
const cors = require('cors');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
require('dotenv').config();

const logger = require('./utils/logger');
const config = require('./config/config');

// 导入路由
const qinglongRoutes = require('./routes/qinglong');
const aiRoutes = require('./routes/ai');
const scriptRoutes = require('./routes/script');
const logRoutes = require('./routes/log');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// 中间件
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// 静态文件服务
app.use('/static', express.static(path.join(__dirname, '../public')));

// 路由
app.use('/api/qinglong', qinglongRoutes);
app.use('/api/ai', aiRoutes);
app.use('/api/script', scriptRoutes);
app.use('/api/log', logRoutes);

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0'
  });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  logger.error('Unhandled error:', err);
  res.status(500).json({
    success: false,
    message: '服务器内部错误',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在'
  });
});

// Socket.IO连接处理
io.on('connection', (socket) => {
  logger.info(`客户端连接: ${socket.id}`);

  socket.on('disconnect', () => {
    logger.info(`客户端断开连接: ${socket.id}`);
  });

  // 加入日志房间
  socket.on('join-log-room', () => {
    socket.join('logs');
    logger.info(`客户端 ${socket.id} 加入日志房间`);

    // 发送最近的日志
    const recentLogs = logger.getMemoryLogs({ page: 1, pageSize: 10 });
    socket.emit('recent-logs', recentLogs.logs);
  });

  // 离开日志房间
  socket.on('leave-log-room', () => {
    socket.leave('logs');
    logger.info(`客户端 ${socket.id} 离开日志房间`);
  });

  // 加入状态房间
  socket.on('join-status-room', () => {
    socket.join('status');
    logger.info(`客户端 ${socket.id} 加入状态房间`);
  });

  // 离开状态房间
  socket.on('leave-status-room', () => {
    socket.leave('status');
    logger.info(`客户端 ${socket.id} 离开状态房间`);
  });
});

// 将io实例挂载到app上，供其他模块使用
app.set('io', io);
global.io = io;

const PORT = process.env.PORT || 3000;

server.listen(PORT, () => {
  logger.info(`服务器启动成功，端口: ${PORT}`);
  logger.info(`环境: ${process.env.NODE_ENV || 'development'}`);
});

// 优雅关闭
process.on('SIGTERM', () => {
  logger.info('收到SIGTERM信号，正在关闭服务器...');
  server.close(() => {
    logger.info('服务器已关闭');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('收到SIGINT信号，正在关闭服务器...');
  server.close(() => {
    logger.info('服务器已关闭');
    process.exit(0);
  });
});

module.exports = app;
