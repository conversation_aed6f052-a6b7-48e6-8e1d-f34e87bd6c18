<template>
  <div class="not-found">
    <div class="not-found-content">
      <div class="not-found-icon">
        <el-icon><WarningFilled /></el-icon>
      </div>
      <h1 class="not-found-title">404</h1>
      <p class="not-found-description">抱歉，您访问的页面不存在</p>
      <el-button type="primary" @click="$router.push('/')">
        返回首页
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { WarningFilled } from '@element-plus/icons-vue'
</script>

<style lang="scss" scoped>
.not-found {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--el-bg-color-page);
  
  .not-found-content {
    text-align: center;
    
    .not-found-icon {
      font-size: 120px;
      color: var(--el-color-warning);
      margin-bottom: 20px;
    }
    
    .not-found-title {
      font-size: 72px;
      font-weight: bold;
      color: var(--el-text-color-primary);
      margin: 0 0 16px 0;
    }
    
    .not-found-description {
      font-size: 18px;
      color: var(--el-text-color-secondary);
      margin: 0 0 32px 0;
    }
  }
}
</style>
