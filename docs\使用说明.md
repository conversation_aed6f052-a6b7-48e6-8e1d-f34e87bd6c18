# 青龙面板管理界面 - 使用说明

## 功能概览

青龙面板管理界面是一个基于Web的可视化管理工具，提供以下核心功能：

- 🔗 **无需登录**：直接通过青龙面板的Client ID和Client Secret进行连接认证
- 📊 **消息面板**：实时展示系统日志和任务执行状态，支持分级显示和搜索
- 🤖 **AI脚本生成器**：支持多AI模型，中文自然语言输入生成脚本
- 🚀 **一键部署**：脚本上传、批量操作、Cron配置、版本管理
- ⚙️ **连接设置**：青龙面板和AI服务配置管理

## 界面导航

### 主要页面

1. **仪表板**：系统概览和快速操作
2. **消息面板**：日志查看和监控
3. **AI脚本生成器**：智能脚本生成
4. **一键部署**：脚本管理和部署
5. **连接设置**：系统配置

### 顶部导航栏

- **折叠按钮**：收起/展开侧边栏
- **面包屑导航**：显示当前页面位置
- **连接状态**：显示青龙面板连接状态
- **主题切换**：深色/浅色主题切换
- **系统菜单**：系统信息和关于页面

## 详细功能说明

### 1. 仪表板

#### 统计卡片
- **脚本总数**：显示青龙面板中的脚本数量
- **定时任务**：显示定时任务数量
- **环境变量**：显示环境变量数量
- **连接状态**：显示青龙面板连接状态

#### 快速操作
- **AI生成脚本**：跳转到AI脚本生成器
- **一键部署**：跳转到部署页面
- **查看日志**：跳转到消息面板
- **连接设置**：跳转到设置页面

#### 系统状态
- **青龙面板**：连接状态和版本信息
- **AI服务**：AI服务配置状态
- **WebSocket**：实时通信连接状态

#### 最近活动
- 显示最近的系统日志
- 支持实时更新
- 点击"查看全部"跳转到消息面板

### 2. 消息面板

#### 日志筛选
- **日志级别**：错误、警告、信息、成功
- **日志来源**：系统、青龙面板、AI服务、脚本
- **关键词搜索**：支持模糊搜索

#### 日志显示
- **时间戳**：精确到秒的时间显示
- **级别标识**：彩色标识不同日志级别
- **来源标识**：显示日志来源模块
- **消息内容**：详细的日志信息

#### 日志操作
- **刷新**：手动刷新日志列表
- **清空**：清空所有内存日志
- **实时更新**：通过WebSocket实时接收新日志
- **分页浏览**：支持分页查看历史日志

### 3. AI脚本生成器

#### 脚本模板
- **每日签到类**：用于各种平台的每日签到脚本
- **商品监控类**：用于监控商品价格、库存等信息
- **自动抢购类**：用于自动抢购商品的脚本
- **数据采集类**：用于采集网站数据的脚本

#### 生成流程
1. **选择模板**：选择合适的脚本模板（可选）
2. **描述需求**：用中文详细描述脚本需求
3. **生成脚本**：AI根据需求生成JavaScript代码
4. **查看结果**：在右侧查看生成的脚本代码
5. **保存脚本**：将生成的脚本保存到本地

#### 生成历史
- 保存最近50次生成记录
- 支持收藏重要的生成结果
- 可以重新编辑和优化历史记录

### 4. 一键部署

#### 本地脚本管理
- **脚本列表**：显示所有本地保存的脚本
- **脚本信息**：文件名、大小、修改时间
- **多选操作**：支持批量选择脚本

#### 部署配置
- **创建定时任务**：可选择是否同时创建定时任务
- **任务名称**：自定义定时任务名称
- **执行时间**：支持每天、每周、每月、自定义
- **Cron表达式**：支持自定义Cron表达式
- **任务描述**：添加任务说明

#### 部署操作
- **一键部署**：将选中的脚本上传到青龙面板
- **批量操作**：支持同时部署多个脚本
- **部署结果**：显示每个脚本的部署状态
- **错误处理**：显示部署失败的详细原因

### 5. 连接设置

#### 青龙面板配置
- **面板地址**：青龙面板的完整URL地址
- **Client ID**：从青龙面板应用设置获取
- **Client Secret**：从青龙面板应用设置获取
- **测试连接**：验证配置是否正确
- **保存配置**：保存连接信息到本地

#### AI服务配置
- **AI提供商**：OpenAI、Claude、本地Ollama
- **API密钥**：对应服务的API密钥
- **API地址**：自定义API地址（可选）
- **模型名称**：自定义模型名称（可选）
- **测试连接**：验证AI服务配置

#### 系统信息
- **应用版本**：当前应用版本号
- **运行环境**：开发/生产环境
- **启动时间**：应用启动时间
- **连接状态**：各服务连接状态

## 使用技巧

### 1. 高效使用AI生成器

**最佳实践**：
- 详细描述需求，包括具体的网站、功能、参数
- 提供示例数据或API接口信息
- 说明错误处理和日志输出要求
- 指定需要的环境变量

**示例描述**：
```
创建一个京东签到脚本，需要以下功能：
1. 使用COOKIE环境变量进行身份验证
2. 访问京东签到接口进行签到
3. 获取签到结果和积分信息
4. 输出详细的执行日志
5. 支持多账号（COOKIE用&分隔）
6. 添加随机延时避免频率限制
```

### 2. 脚本部署最佳实践

**部署前检查**：
- 确认脚本语法正确
- 检查所需的环境变量
- 验证外部依赖是否可用
- 测试脚本在本地是否正常运行

**定时任务设置**：
- 避免在整点时间执行（如00:00）
- 添加随机分钟数分散执行时间
- 考虑网站的访问高峰期
- 设置合理的执行频率

### 3. 日志监控技巧

**日志筛选**：
- 使用级别筛选快速定位问题
- 通过来源筛选查看特定模块日志
- 使用关键词搜索定位具体问题

**问题排查**：
1. 查看错误级别日志
2. 检查相关时间段的所有日志
3. 关注警告信息可能的潜在问题
4. 结合青龙面板日志进行对比

### 4. 系统维护建议

**定期维护**：
- 定期清理日志文件
- 检查脚本执行状态
- 更新AI模型配置
- 备份重要脚本文件

**性能优化**：
- 合理设置日志级别
- 定期清空内存日志
- 避免同时运行过多脚本
- 监控系统资源使用情况

## 常见使用场景

### 场景1：新用户首次使用

1. 访问管理界面
2. 进入"连接设置"配置青龙面板
3. 测试连接确保配置正确
4. 在"AI脚本生成器"中生成第一个脚本
5. 使用"一键部署"将脚本部署到青龙面板
6. 在"消息面板"中查看执行日志

### 场景2：批量管理脚本

1. 在AI生成器中生成多个脚本
2. 在部署页面选择多个脚本
3. 配置统一的定时任务设置
4. 执行批量部署
5. 查看部署结果和错误信息

### 场景3：问题排查

1. 在仪表板中发现异常状态
2. 进入消息面板查看错误日志
3. 使用筛选功能定位问题来源
4. 根据日志信息修复问题
5. 重新部署修复后的脚本

### 场景4：定期维护

1. 检查系统连接状态
2. 清理过期的日志记录
3. 更新脚本版本
4. 优化定时任务配置
5. 备份重要配置和脚本

## 注意事项

### 安全注意事项

1. **保护敏感信息**：不要在脚本中硬编码密码和密钥
2. **使用环境变量**：敏感配置通过环境变量传递
3. **定期更换密钥**：定期更新API密钥和访问凭据
4. **限制访问权限**：合理设置青龙面板应用权限

### 使用限制

1. **API频率限制**：注意AI服务的API调用频率限制
2. **脚本执行频率**：避免过于频繁的脚本执行
3. **资源使用**：监控系统资源使用情况
4. **网络依赖**：确保网络连接稳定

### 兼容性说明

1. **青龙面板版本**：建议使用2.10.0及以上版本
2. **浏览器支持**：支持现代浏览器（Chrome、Firefox、Safari、Edge）
3. **Node.js版本**：需要Node.js 16.0.0及以上版本
4. **网络环境**：需要能够访问青龙面板和AI服务
