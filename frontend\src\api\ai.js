import request from './request'

export const aiApi = {
  // 配置AI服务
  configAI(data) {
    return request.post('/ai/config', data)
  },
  
  // 测试AI服务连接
  testAI(data) {
    return request.post('/ai/test', data)
  },
  
  // 生成脚本
  generateScript(data) {
    return request.post('/ai/generate', data, {
      timeout: 60000 // AI生成可能需要更长时间
    })
  },
  
  // 优化脚本
  optimizeScript(data) {
    return request.post('/ai/optimize', data, {
      timeout: 60000
    })
  },
  
  // 解释代码
  explainCode(data) {
    return request.post('/ai/explain', data, {
      timeout: 60000
    })
  },
  
  // 获取脚本模板
  getTemplates() {
    return request.get('/ai/templates')
  },
  
  // 获取生成历史
  getHistory(params) {
    return request.get('/ai/history', { params })
  },
  
  // 保存生成记录
  saveHistory(data) {
    return request.post('/ai/history', data)
  },
  
  // 删除生成记录
  deleteHistory(id) {
    return request.delete(`/ai/history/${id}`)
  },
  
  // 收藏/取消收藏
  toggleFavorite(id, isFavorite) {
    return request.put(`/ai/history/${id}/favorite`, { isFavorite })
  },
  
  // 语音转文字
  speechToText(data) {
    return request.post('/ai/speech-to-text', data)
  },
  
  // 代码验证
  validateCode(data) {
    return request.post('/ai/validate', data)
  }
}
