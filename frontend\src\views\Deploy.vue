<template>
  <div class="deploy">
    <el-row :gutter="20">
      <!-- 左侧：脚本列表 -->
      <el-col :xs="24" :lg="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span class="title">本地脚本</span>
              <el-button
                type="primary"
                size="small"
                @click="refreshScripts"
                :loading="loading"
              >
                刷新
              </el-button>
            </div>
          </template>
          
          <div class="scripts-list">
            <div v-if="scripts.length === 0 && !loading" class="empty-scripts">
              <el-empty description="暂无本地脚本">
                <el-button type="primary" @click="$router.push('/ai-generator')">
                  去生成脚本
                </el-button>
              </el-empty>
            </div>
            
            <div v-loading="loading" class="scripts-container">
              <div
                v-for="script in scripts"
                :key="script.filename"
                class="script-item"
                :class="{ selected: selectedScripts.includes(script.filename) }"
                @click="toggleScript(script.filename)"
              >
                <div class="script-info">
                  <div class="script-name">{{ script.name }}</div>
                  <div class="script-meta">
                    <span class="script-size">{{ formatSize(script.size) }}</span>
                    <span class="script-time">{{ formatTime(script.modified) }}</span>
                  </div>
                </div>
                <div class="script-actions">
                  <el-checkbox
                    :model-value="selectedScripts.includes(script.filename)"
                    @change="toggleScript(script.filename)"
                  />
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 右侧：部署配置 -->
      <el-col :xs="24" :lg="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span class="title">部署配置</span>
              <el-tag type="info" size="small">{{ selectedScripts.length }} 个脚本</el-tag>
            </div>
          </template>
          
          <div class="deploy-config">
            <el-form label-width="120px">
              <el-form-item label="创建定时任务">
                <el-switch v-model="createCron" />
              </el-form-item>
              
              <template v-if="createCron">
                <el-form-item label="任务名称">
                  <el-input
                    v-model="cronConfig.name"
                    placeholder="定时任务名称"
                  />
                </el-form-item>
                
                <el-form-item label="执行时间">
                  <el-select
                    v-model="cronType"
                    placeholder="选择执行频率"
                    style="width: 100%"
                  >
                    <el-option label="每天执行" value="daily" />
                    <el-option label="每周执行" value="weekly" />
                    <el-option label="每月执行" value="monthly" />
                    <el-option label="自定义" value="custom" />
                  </el-select>
                </el-form-item>
                
                <el-form-item label="Cron表达式" v-if="cronType === 'custom'">
                  <el-input
                    v-model="cronConfig.schedule"
                    placeholder="0 0 * * *"
                  />
                </el-form-item>
                
                <el-form-item label="任务描述">
                  <el-input
                    v-model="cronConfig.description"
                    type="textarea"
                    :rows="3"
                    placeholder="任务描述（可选）"
                  />
                </el-form-item>
              </template>
              
              <el-form-item>
                <el-button
                  type="primary"
                  :icon="Upload"
                  @click="deployScripts"
                  :loading="deploying"
                  :disabled="selectedScripts.length === 0"
                >
                  一键部署
                </el-button>
                <el-button @click="clearSelection">
                  清空选择
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-card>
        
        <!-- 部署结果 -->
        <el-card class="mt-4" v-if="deployResults.length > 0">
          <template #header>
            <div class="card-header">
              <span class="title">部署结果</span>
            </div>
          </template>
          
          <div class="deploy-results">
            <div
              v-for="result in deployResults"
              :key="result.filename"
              class="result-item"
            >
              <div class="result-info">
                <span class="result-name">{{ result.filename }}</span>
                <el-tag
                  :type="result.success ? 'success' : 'danger'"
                  size="small"
                >
                  {{ result.success ? '成功' : '失败' }}
                </el-tag>
              </div>
              <div class="result-message" v-if="result.message">
                {{ result.message }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useQinglongStore } from '@/stores/qinglong'
import { Upload } from '@element-plus/icons-vue'

const qinglongStore = useQinglongStore()

// 响应式数据
const scripts = ref([])
const selectedScripts = ref([])
const loading = ref(false)
const deploying = ref(false)
const createCron = ref(false)
const cronType = ref('daily')
const deployResults = ref([])

const cronConfig = reactive({
  name: '',
  schedule: '0 0 * * *',
  description: ''
})

// 方法
const refreshScripts = async () => {
  try {
    loading.value = true
    
    // 模拟获取本地脚本列表
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    scripts.value = [
      {
        filename: 'example1.js',
        name: '示例脚本1',
        size: 1024,
        modified: new Date().toISOString()
      },
      {
        filename: 'example2.js',
        name: '示例脚本2',
        size: 2048,
        modified: new Date().toISOString()
      }
    ]
  } catch (error) {
    console.error('获取脚本列表失败:', error)
  } finally {
    loading.value = false
  }
}

const toggleScript = (filename) => {
  const index = selectedScripts.value.indexOf(filename)
  if (index > -1) {
    selectedScripts.value.splice(index, 1)
  } else {
    selectedScripts.value.push(filename)
  }
}

const clearSelection = () => {
  selectedScripts.value = []
}

const deployScripts = async () => {
  if (!qinglongStore.isConnected) {
    ElMessage.error('请先连接青龙面板')
    return
  }
  
  try {
    deploying.value = true
    
    // 模拟部署过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 模拟部署结果
    deployResults.value = selectedScripts.value.map(filename => ({
      filename,
      success: Math.random() > 0.3, // 70%成功率
      message: Math.random() > 0.3 ? '部署成功' : '部署失败：网络错误'
    }))
    
    const successCount = deployResults.value.filter(r => r.success).length
    const totalCount = deployResults.value.length
    
    if (successCount === totalCount) {
      ElMessage.success(`所有脚本部署成功 (${successCount}/${totalCount})`)
    } else {
      ElMessage.warning(`部分脚本部署成功 (${successCount}/${totalCount})`)
    }
    
    // 清空选择
    selectedScripts.value = []
  } catch (error) {
    console.error('部署失败:', error)
    ElMessage.error('部署失败，请稍后重试')
  } finally {
    deploying.value = false
  }
}

const formatSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  return date.toLocaleString()
}

// 生命周期
onMounted(() => {
  refreshScripts()
})
</script>

<style lang="scss" scoped>
.deploy {
  .scripts-list {
    .empty-scripts {
      padding: 40px 0;
    }
    
    .scripts-container {
      max-height: 400px;
      overflow-y: auto;
      
      .script-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px;
        border: 1px solid var(--el-border-color-lighter);
        border-radius: 6px;
        margin-bottom: 8px;
        cursor: pointer;
        transition: all 0.3s;
        
        &:hover {
          border-color: var(--el-color-primary);
          background-color: var(--el-color-primary-light-9);
        }
        
        &.selected {
          border-color: var(--el-color-primary);
          background-color: var(--el-color-primary-light-9);
        }
        
        .script-info {
          flex: 1;
          
          .script-name {
            font-size: 14px;
            font-weight: 500;
            color: var(--el-text-color-primary);
            margin-bottom: 4px;
          }
          
          .script-meta {
            font-size: 12px;
            color: var(--el-text-color-secondary);
            
            .script-size {
              margin-right: 12px;
            }
          }
        }
        
        .script-actions {
          .el-checkbox {
            pointer-events: none;
          }
        }
      }
    }
  }
  
  .deploy-config {
    .el-form-item {
      margin-bottom: 20px;
    }
  }
  
  .deploy-results {
    .result-item {
      padding: 12px;
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 6px;
      margin-bottom: 8px;
      
      .result-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 4px;
        
        .result-name {
          font-size: 14px;
          font-weight: 500;
          color: var(--el-text-color-primary);
        }
      }
      
      .result-message {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }
  }
}
</style>
